# PPT截屏监控工具技术文档

## 1. 项目概述

PPT截屏监控工具是一款基于Python开发的自动截图软件，主要用于会议、培训等场景中自动捕获PPT幻灯片的变化。本项目使用Tkinter构建图形界面，OpenCV进行图像处理，并集成了用户管理、水印添加和PDF导出等功能。

## 2. 系统架构

系统由以下几个核心组件构成：

1. **主程序入口**：负责程序初始化和启动
2. **用户认证系统**：处理用户登录和权限验证
3. **监控引擎**：负责检测屏幕变化并截图
4. **图像处理模块**：添加水印和图像转换
5. **图形用户界面**：提供用户交互界面
6. **导出功能**：将截图导出为PDF

### 2.1 类图关系

```
+----------------+     +----------------+     +----------------+
|   LoginDialog  |---->| PPTMonitorGUI  |<--->|   PPTMonitor   |
+----------------+     +----------------+     +----------------+
                             |
                             v
                       +----------------+
                       |    AppConfig   |
                       +----------------+
```

## 3. 主要类和模块

### 3.1 AppConfig 类

配置参数管理类，集中存储应用程序的各种配置项。

```python
class AppConfig:
    # 监控设置
    MONITOR_INTERVAL = 1  # 截屏检测间隔(秒)
    SIMILARITY_THRESHOLD = 0.95  # 画面变化阈值
    
    # 水印设置
    WATERMARK_TEXT = "内部资料，禁止外传"
    WATERMARK_FONT_SIZE = 20
    WATERMARK_COLOR = (0, 0, 0, 32)  # RGBA
    WATERMARK_SPACING = 200  # 水印间距(像素)
    WATERMARK_ANGLE = 30  # 旋转角度
    WATERMARK_ENABLED = True  # 是否启用水印
    
    # PDF和预览设置...
```

### 3.2 LoginDialog 类

基于Tkinter的登录对话框，继承自`simpledialog.Dialog`，处理用户身份验证。

主要方法：
- `body()`: 创建登录界面元素
- `apply()`: 应用登录结果
- `validate()`: 验证用户凭据

### 3.3 PPTMonitor 类

核心监控引擎，负责截屏、图像相似度比较和水印添加。

主要方法：
- `start_monitoring()`: 启动监控线程
- `is_similar()`: 计算两张图像的相似度
- `add_watermark()`: 添加带有用户信息的水印
- `save_selected_as_pdf()`: 将选中的图像保存为PDF

### 3.4 PPTMonitorGUI 类

图形用户界面，提供用户交互功能，管理UI组件和事件处理。

主要方法：
- `setup_ui()`: 创建界面组件
- `create_menu()`: 创建菜单栏
- `toggle_monitoring()`: 控制监控开始/停止
- `preview_screenshots()`: 预览截图
- `show_full_image()`: 显示大图预览
- `apply_settings()`: 应用用户设置

## 4. 核心功能实现

### 4.1 屏幕变化检测算法

系统使用结构相似性(SSIM)算法来检测屏幕变化：

```python
def is_similar(self, img1, img2):
    """计算两张图片的相似度"""
    if img1.shape != img2.shape:
        return False

    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

    try:
        from skimage.metrics import structural_similarity as ssim
        score = ssim(gray1, gray2)
    except ImportError:
        try:
            score = cv2.quality.QualitySSIM_compute(gray1, gray2)[0]
        except:
            diff = cv2.absdiff(gray1, gray2)
            _, diff = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)
            score = 1.0 - (np.count_nonzero(diff) / float(diff.size))

    return score > self.similarity_threshold
```

算法首先将图像转换为灰度，然后使用SSIM计算相似度。如果无法使用SSIM，则回退到像素差异比较。

### 4.2 水印添加实现

水印添加功能使用PIL库实现：

1. 创建包含用户名和时间的水印文本
2. 使用半透明颜色绘制文本
3. 按指定角度旋转水印
4. 以网格方式分布在整个图像上

```python
def add_watermark(self, image):
    """添加全屏水印"""
    if not AppConfig.WATERMARK_ENABLED:
        return image

    img = image.copy()
    draw = ImageDraw.Draw(img, 'RGBA')
    
    # 字体加载和水印文本生成...
    
    # 在图像上以网格模式添加水印
    for x in range(0, width, max(text_width1, text_width2) + AppConfig.WATERMARK_SPACING):
        for y in range(0, height, text_height1 + text_height2 + line_spacing + AppConfig.WATERMARK_SPACING):
            # 创建水印文本图像
            txt_img = Image.new('RGBA', ...)
            # 绘制文本...
            # 旋转...
            # 粘贴到原图...
```

### 4.3 PDF导出功能

使用FPDF库实现，关键特性：

1. 支持自定义页面尺寸（保持16:9比例）
2. 图像自适应缩放，保持原始比例
3. 多线程处理，避免UI卡顿
4. 内存优化，使用BytesIO处理图像数据

## 5. 用户界面设计

### 5.1 主界面布局

主界面采用分区域设计：
- 顶部：标题
- 中部：控制面板、状态显示
- 底部：预览按钮和版权信息

### 5.2 预览界面设计

预览界面使用网格布局显示缩略图，支持滚动浏览和缩放预览：
- 每行显示固定数量的缩略图
- 支持选择/取消选择功能
- 大图预览支持键盘导航
- 导出操作在后台线程执行，显示进度条

## 6. 安全机制

### 6.1 用户认证

系统内置固定账号密码，通过LoginDialog实现认证:

```python
def validate(self):
    username, password = self.username_var.get(), self.password_var.get()
    if username in ACCOUNTS and ACCOUNTS[username] == password:
        return True
    else:
        messagebox.showerror("登录失败", "用户名或密码错误")
        return False
```

### 6.2 水印保护

所有截图均添加水印，确保内容可追溯：
- 固定文本警示
- 包含截图用户信息
- 添加时间戳
- 超级密码保护（关闭水印功能需要管理员密码）

## 7. 已知问题和优化方向

### 7.1 已知问题

1. **缩略图显示问题**：在图像数量多时可能出现内存问题
2. **Tkinter图像引用**：需要手动保持引用以防止垃圾回收
3. **水印字体加载**：部分系统可能找不到指定字体，需要回退机制

### 7.2 优化方向

1. **数据持久化**：实现截图会话保存和恢复功能
2. **图像压缩**：优化内存占用，支持更多截图
3. **自动更新**：增加版本检查和自动更新机制
4. **区域监控**：支持选择性监控屏幕区域
5. **OCR集成**：添加文字识别功能，提取PPT内容

## 8. 开发环境和依赖

- Python 3.8+
- OpenCV 4.x
- Pillow 8.x+
- FPDF 1.7.2+
- Numpy 1.19+
- PyAutoGUI 0.9.52+

## 9. 部署说明

### 9.1 打包方法

推荐使用PyInstaller打包为可执行文件：

```
pyinstaller --onefile --windowed --icon=icon.ico main.py
```

### 9.2 安装步骤

1. 安装Python 3.8或更高版本
2. 安装依赖库：`pip install opencv-python numpy pillow fpdf pyautogui`
3. 运行主程序：`python main.py`

## 10. 维护和扩展

### 10.1 添加新用户

编辑`ACCOUNTS`字典添加新用户：

```python
ACCOUNTS = {
    "新用户名": "密码",
    # 其他用户...
}
```

### 10.2 自定义水印

修改`AppConfig`类中的水印相关参数：

```python
# 水印设置
WATERMARK_TEXT = "自定义水印文本"
WATERMARK_FONT_SIZE = 20  # 调整大小
WATERMARK_COLOR = (R, G, B, A)  # 调整颜色和透明度
```

### 10.3 添加新功能

系统设计遵循模块化原则，可按以下步骤扩展：

1. 在相应类中添加新方法
2. 在GUI中添加界面元素
3. 连接事件处理函数
4. 更新AppConfig添加新配置项 
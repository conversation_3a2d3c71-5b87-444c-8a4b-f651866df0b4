# PPT截屏监控工具使用说明书

## 1. 软件简介

PPT截屏监控工具是一款专为会议、培训、教学等场景设计的自动截图软件。该工具能够自动检测PowerPoint演示文稿的画面变化，并在变化时自动截取屏幕图像，避免了手动截图的繁琐操作，让用户能够专注于会议内容。所有截图均自动添加水印，包含用户信息和时间戳，确保信息安全。

## 2. 主要功能

- **自动截屏**：监控屏幕变化，仅在PPT内容变化时才截取图像
- **水印保护**：为截图自动添加水印，包含内部资料说明、用户名和时间戳
- **用户验证**：多用户登录系统，不同用户的截图带有对应的用户标识
- **图像预览**：可以预览、选择并导出所需的截图
- **PDF导出**：将选中的截图保存为PDF文档，便于分享和归档
- **参数设置**：可调整监控敏感度、截图间隔等参数

## 3. 系统要求

- 操作系统：Windows 10及以上
- Python 3.8及以上
- 依赖库：opencv-python, numpy, pillow, fpdf, pyautogui

## 4. 快速入门

### 4.1 登录系统

启动软件后，首先需要登录：

1. 在登录窗口输入用户名和密码
2. 系统内置以下账号（用户名/密码）：
   - 管理员：123456
   - 办公室：123456
   - 会议室：123456
   - 覃瀚华：123456
   - 方俞惠：123456
   - 梁立斌：123456
   - 邹昌光：123456
   - 师文清：123456

### 4.2 基本操作

登录成功后，主界面包含以下操作区域：

- **控制面板**：包含"开始监控"和"暂停监控"按钮
- **当前状态**：显示监控状态和已截图数量
- **预览按钮**：点击"结束并预览截图"可查看所有截取的图像

### 4.3 监控操作流程

1. 点击"开始监控"按钮开始自动截屏
2. 系统将自动检测屏幕变化并截图
3. 监控过程中可随时点击"暂停监控"按钮暂停监控
4. 完成监控后，点击"结束并预览截图"查看所有截图
5. 在预览界面选择需要的截图，点击"保存选中为PDF"导出为PDF文档

## 5. 功能详解

### 5.1 监控设置

通过菜单栏"设置→监控参数设置"可以调整以下参数：

- **检测间隔**：调整屏幕检查的时间间隔（秒），默认为1秒
- **变化阈值**：调整画面变化的敏感度（0.8-0.99），值越大表示对变化越不敏感，默认为0.95
- **水印开关**：选择是否在截图上添加水印（关闭需要超级密码）

### 5.2 账户管理

通过菜单栏"设置→修改密码"可以修改当前登录用户的密码：

1. 输入旧密码
2. 输入新密码
3. 确认新密码
4. 点击"确认"完成修改

### 5.3 截图预览

预览界面提供以下功能：

- 缩略图浏览：查看所有截取的图像
- 双击图片可查看大图
- 全选/全不选/反选：快速选择需要的截图
- 保存为PDF：将选中的截图导出为PDF文档

### 5.4 水印功能

所有截图都会自动添加水印，水印内容包括：

- 固定提示："内部资料，禁止外传"
- 当前用户名
- 截图时间（年-月-日 时:分:秒）

关闭水印功能需要超级密码（管理员使用）。

## 6. 菜单项说明

### 6.1 文件菜单

- **退出**：关闭软件

### 6.2 设置菜单

- **监控参数设置**：调整监控参数
- **修改密码**：修改当前用户密码

### 6.3 帮助菜单

- **关于**：显示软件版本和版权信息

## 7. 常见问题

### Q1: 为什么我的截图数量很少？
A1: 本软件基于画面变化进行截图，如果PPT页面切换不频繁，或阈值设置过高，可能导致截图数量减少。尝试调低"变化阈值"参数。

### Q2: 如何关闭水印功能？
A2: 在"监控参数设置"中取消勾选"启用水印"，需要输入超级密码（Huagev587@）。

### Q3: 截图预览时缩略图无法显示怎么办？
A3: 这可能是由于内存限制导致的问题。尝试减少单次监控的时长，或者分批次保存截图。

### Q4: 如何一次性导出所有截图？
A4: 在预览界面点击"全选"按钮，然后点击"保存选中为PDF"。

## 8. 快捷键

- **Esc**：在预览大图时关闭当前窗口
- **左/右方向键**：在预览大图时浏览上一张/下一张图片

## 9. 技术支持

如有任何问题或建议，请联系技术支持：

- 邮箱：<EMAIL>
- 电话：123-456-7890

## 10. 版权信息

© 2025 覃瀚华 | 版本: 1.5

本软件仅供内部使用，未经授权不得外传或商用。 
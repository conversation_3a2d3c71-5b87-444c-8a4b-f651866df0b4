('D:\\pythonProject\\ppt_capture\\build\\PPT截屏监控工具\\PYZ-00.pyz',
 [('PIL',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'c:\\program files\\python38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'c:\\program files\\python38\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\program files\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\program files\\python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_markupbase',
   'c:\\program files\\python38\\lib\\_markupbase.py',
   'PYMODULE'),
  ('_osx_support',
   'c:\\program files\\python38\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python38\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'c:\\program files\\python38\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'c:\\program files\\python38\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'c:\\program files\\python38\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'c:\\program files\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\program files\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\program files\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\program files\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\program files\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\program files\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\program files\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\program files\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\program files\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\program files\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\program files\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\program files\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\program files\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\program files\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\program files\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\program files\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\program files\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\program files\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\program files\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\program files\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\program files\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\program files\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\program files\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\program files\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\program files\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\program files\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\program files\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\program files\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'c:\\program files\\python38\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'c:\\program files\\python38\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'c:\\program files\\python38\\lib\\bisect.py', 'PYMODULE'),
  ('bs4',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'c:\\program files\\python38\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'c:\\program files\\python38\\lib\\calendar.py', 'PYMODULE'),
  ('cgi', 'c:\\program files\\python38\\lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\program files\\python38\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'c:\\program files\\python38\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'c:\\program files\\python38\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\program files\\python38\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'c:\\program files\\python38\\lib\\colorsys.py', 'PYMODULE'),
  ('commctrl',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\program files\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'c:\\program files\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python38\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'c:\\program files\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'c:\\program files\\python38\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'c:\\program files\\python38\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'c:\\program files\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\program files\\python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\program files\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\program files\\python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\program files\\python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\program files\\python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'c:\\program files\\python38\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'c:\\program files\\python38\\lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'c:\\program files\\python38\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'c:\\program files\\python38\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'c:\\program files\\python38\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\program files\\python38\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\program files\\python38\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'c:\\program files\\python38\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\program files\\python38\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\program files\\python38\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\program files\\python38\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\program files\\python38\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\program files\\python38\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\program files\\python38\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\program files\\python38\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\program files\\python38\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('doctest', 'c:\\program files\\python38\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'c:\\program files\\python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('fontTools',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\__init__.py',
   'PYMODULE'),
  ('fontTools.agl',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\agl.py',
   'PYMODULE'),
  ('fontTools.cffLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFF2ToCFF',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\CFF2ToCFF.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFFToCFF2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\CFFToCFF2.py',
   'PYMODULE'),
  ('fontTools.cffLib.specializer',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\specializer.py',
   'PYMODULE'),
  ('fontTools.cffLib.transforms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\transforms.py',
   'PYMODULE'),
  ('fontTools.cffLib.width',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\width.py',
   'PYMODULE'),
  ('fontTools.colorLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.colorLib.builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.errors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\errors.py',
   'PYMODULE'),
  ('fontTools.colorLib.geometry',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\geometry.py',
   'PYMODULE'),
  ('fontTools.colorLib.table_builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\table_builder.py',
   'PYMODULE'),
  ('fontTools.colorLib.unbuilder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\unbuilder.py',
   'PYMODULE'),
  ('fontTools.config',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\config\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.split',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\split.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.statNames',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\statNames.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.types',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\types.py',
   'PYMODULE'),
  ('fontTools.encodings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\encodings\\__init__.py',
   'PYMODULE'),
  ('fontTools.encodings.StandardEncoding',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\encodings\\StandardEncoding.py',
   'PYMODULE'),
  ('fontTools.encodings.codecs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\encodings\\codecs.py',
   'PYMODULE'),
  ('fontTools.feaLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.ast',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\feaLib\\ast.py',
   'PYMODULE'),
  ('fontTools.feaLib.error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\error.py',
   'PYMODULE'),
  ('fontTools.feaLib.location',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\location.py',
   'PYMODULE'),
  ('fontTools.feaLib.lookupDebugInfo',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\lookupDebugInfo.py',
   'PYMODULE'),
  ('fontTools.misc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.arrayTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\arrayTools.py',
   'PYMODULE'),
  ('fontTools.misc.classifyTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\classifyTools.py',
   'PYMODULE'),
  ('fontTools.misc.cliTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\cliTools.py',
   'PYMODULE'),
  ('fontTools.misc.configTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\configTools.py',
   'PYMODULE'),
  ('fontTools.misc.cython',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\cython.py',
   'PYMODULE'),
  ('fontTools.misc.dictTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\dictTools.py',
   'PYMODULE'),
  ('fontTools.misc.encodingTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\encodingTools.py',
   'PYMODULE'),
  ('fontTools.misc.etree',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\misc\\etree.py',
   'PYMODULE'),
  ('fontTools.misc.filenames',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\filenames.py',
   'PYMODULE'),
  ('fontTools.misc.fixedTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\fixedTools.py',
   'PYMODULE'),
  ('fontTools.misc.intTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\intTools.py',
   'PYMODULE'),
  ('fontTools.misc.iterTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\iterTools.py',
   'PYMODULE'),
  ('fontTools.misc.lazyTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\lazyTools.py',
   'PYMODULE'),
  ('fontTools.misc.loggingTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\loggingTools.py',
   'PYMODULE'),
  ('fontTools.misc.macCreatorType',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\macCreatorType.py',
   'PYMODULE'),
  ('fontTools.misc.macRes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\macRes.py',
   'PYMODULE'),
  ('fontTools.misc.plistlib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\plistlib\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.psCharStrings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\psCharStrings.py',
   'PYMODULE'),
  ('fontTools.misc.roundTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\roundTools.py',
   'PYMODULE'),
  ('fontTools.misc.sstruct',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\sstruct.py',
   'PYMODULE'),
  ('fontTools.misc.textTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\textTools.py',
   'PYMODULE'),
  ('fontTools.misc.timeTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\timeTools.py',
   'PYMODULE'),
  ('fontTools.misc.transform',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\transform.py',
   'PYMODULE'),
  ('fontTools.misc.treeTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\treeTools.py',
   'PYMODULE'),
  ('fontTools.misc.vector',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\vector.py',
   'PYMODULE'),
  ('fontTools.misc.visitor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\visitor.py',
   'PYMODULE'),
  ('fontTools.misc.xmlReader',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\xmlReader.py',
   'PYMODULE'),
  ('fontTools.misc.xmlWriter',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\xmlWriter.py',
   'PYMODULE'),
  ('fontTools.otlLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\builder.py',
   'PYMODULE'),
  ('fontTools.otlLib.error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\error.py',
   'PYMODULE'),
  ('fontTools.otlLib.maxContextCalc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\maxContextCalc.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\optimize\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize.gpos',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\optimize\\gpos.py',
   'PYMODULE'),
  ('fontTools.pens',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\__init__.py',
   'PYMODULE'),
  ('fontTools.pens.basePen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\basePen.py',
   'PYMODULE'),
  ('fontTools.pens.boundsPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\boundsPen.py',
   'PYMODULE'),
  ('fontTools.pens.filterPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\filterPen.py',
   'PYMODULE'),
  ('fontTools.pens.pointPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\pointPen.py',
   'PYMODULE'),
  ('fontTools.pens.recordingPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\recordingPen.py',
   'PYMODULE'),
  ('fontTools.pens.reverseContourPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\reverseContourPen.py',
   'PYMODULE'),
  ('fontTools.pens.t2CharStringPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\t2CharStringPen.py',
   'PYMODULE'),
  ('fontTools.pens.transformPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\transformPen.py',
   'PYMODULE'),
  ('fontTools.pens.ttGlyphPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\ttGlyphPen.py',
   'PYMODULE'),
  ('fontTools.subset',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\subset\\__init__.py',
   'PYMODULE'),
  ('fontTools.subset.cff',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\subset\\cff.py',
   'PYMODULE'),
  ('fontTools.subset.svg',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\subset\\svg.py',
   'PYMODULE'),
  ('fontTools.subset.util',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\subset\\util.py',
   'PYMODULE'),
  ('fontTools.svgLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.svgLib.path',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\__init__.py',
   'PYMODULE'),
  ('fontTools.svgLib.path.arc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\arc.py',
   'PYMODULE'),
  ('fontTools.svgLib.path.parser',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\parser.py',
   'PYMODULE'),
  ('fontTools.svgLib.path.shapes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\shapes.py',
   'PYMODULE'),
  ('fontTools.ttLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.macUtils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\macUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.reorderGlyphs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\reorderGlyphs.py',
   'PYMODULE'),
  ('fontTools.ttLib.sfnt',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\ttLib\\sfnt.py',
   'PYMODULE'),
  ('fontTools.ttLib.standardGlyphOrder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\standardGlyphOrder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.B_A_S_E_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\B_A_S_E_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.BitmapGlyphMetrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\BitmapGlyphMetrics.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_D_T_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_L_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F__2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_O_L_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_O_L_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_P_A_L_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_P_A_L_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D_S_I_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\D_S_I_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D__e_b_g',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\D__e_b_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.DefaultTable',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\DefaultTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_D_T_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_L_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F_F_T_M_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\F_F_T_M_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F__e_a_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\F__e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_D_E_F_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_D_E_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_M_A_P_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_M_A_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_K_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_K_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_O_S_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_O_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_S_U_B_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_S_U_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_a_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_o_c',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_o_c.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.H_V_A_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\H_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.J_S_T_F_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\J_S_T_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.L_T_S_H_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\L_T_S_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_A_T_H_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_A_T_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_E_T_A_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_E_T_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_V_A_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.O_S_2f_2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\O_S_2f_2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_I_N_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_I_N_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_T_A_T_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_T_A_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_V_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_V_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_f',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_l',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_l.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_B_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_D_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_D_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_J_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_J_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_P_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_S_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_V_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_V_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__0',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__0.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__1',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__1.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__3',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__3.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__5',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__5.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_T_F_A_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_T_F_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.TupleVariation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\TupleVariation.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_A_R_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_A_R_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_D_M_X_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_D_M_X_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_O_R_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_O_R_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_V_A_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_n_k_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_n_k_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._b_s_l_n',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_b_s_l_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_i_d_g',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_i_d_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_m_a_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_m_a_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_e_a_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_p_g_m',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_p_g_m.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_a_s_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_a_s_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_c_i_d',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_c_i_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_l_y_f',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_l_y_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_d_m_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_d_m_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_e_a_d',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_e_a_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_h_e_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_m_t_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._k_e_r_n',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_k_e_r_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_c_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_c_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_o_c_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_o_c_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_t_a_g',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_t_a_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_a_x_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_a_x_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_e_t_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_e_t_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._n_a_m_e',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_n_a_m_e.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._o_p_b_d',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_o_p_b_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_o_s_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_o_s_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_e_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_e_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_o_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_o_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._s_b_i_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_s_b_i_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._t_r_a_k',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_t_r_a_k.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_h_e_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_m_t_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.asciiTable',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\asciiTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.grUtils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\grUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otBase',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otBase.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otConverters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otConverters.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otData',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otData.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTables',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTables.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTraverse',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTraverse.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixGlyph',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixGlyph.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixStrike',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixStrike.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.ttProgram',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\ttProgram.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttCollection',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttCollection.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttFont',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttFont.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttGlyphSet',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttGlyphSet.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttVisitor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttVisitor.py',
   'PYMODULE'),
  ('fontTools.ttLib.woff2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\woff2.py',
   'PYMODULE'),
  ('fontTools.ttx',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\ttx.py',
   'PYMODULE'),
  ('fontTools.unicode',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\unicode.py',
   'PYMODULE'),
  ('fontTools.varLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.varLib.builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\builder.py',
   'PYMODULE'),
  ('fontTools.varLib.cff',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\varLib\\cff.py',
   'PYMODULE'),
  ('fontTools.varLib.errors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\errors.py',
   'PYMODULE'),
  ('fontTools.varLib.featureVars',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\featureVars.py',
   'PYMODULE'),
  ('fontTools.varLib.merger',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\merger.py',
   'PYMODULE'),
  ('fontTools.varLib.models',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\models.py',
   'PYMODULE'),
  ('fontTools.varLib.multiVarStore',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\multiVarStore.py',
   'PYMODULE'),
  ('fontTools.varLib.mvar',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\mvar.py',
   'PYMODULE'),
  ('fontTools.varLib.stat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\stat.py',
   'PYMODULE'),
  ('fontTools.varLib.varStore',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\varStore.py',
   'PYMODULE'),
  ('fpdf',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\__init__.py',
   'PYMODULE'),
  ('fpdf.actions',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\actions.py',
   'PYMODULE'),
  ('fpdf.annotations',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\annotations.py',
   'PYMODULE'),
  ('fpdf.bidi',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\bidi.py',
   'PYMODULE'),
  ('fpdf.deprecation',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\deprecation.py',
   'PYMODULE'),
  ('fpdf.drawing',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\drawing.py',
   'PYMODULE'),
  ('fpdf.encryption',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\encryption.py',
   'PYMODULE'),
  ('fpdf.enums',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\enums.py',
   'PYMODULE'),
  ('fpdf.errors',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\errors.py',
   'PYMODULE'),
  ('fpdf.fonts',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\fonts.py',
   'PYMODULE'),
  ('fpdf.fpdf',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\fpdf.py',
   'PYMODULE'),
  ('fpdf.graphics_state',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\graphics_state.py',
   'PYMODULE'),
  ('fpdf.html',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\html.py',
   'PYMODULE'),
  ('fpdf.image_datastructures',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fpdf\\image_datastructures.py',
   'PYMODULE'),
  ('fpdf.image_parsing',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\image_parsing.py',
   'PYMODULE'),
  ('fpdf.line_break',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\line_break.py',
   'PYMODULE'),
  ('fpdf.linearization',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\linearization.py',
   'PYMODULE'),
  ('fpdf.outline',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\outline.py',
   'PYMODULE'),
  ('fpdf.output',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\output.py',
   'PYMODULE'),
  ('fpdf.prefs',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\prefs.py',
   'PYMODULE'),
  ('fpdf.recorder',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\recorder.py',
   'PYMODULE'),
  ('fpdf.sign',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\sign.py',
   'PYMODULE'),
  ('fpdf.structure_tree',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\structure_tree.py',
   'PYMODULE'),
  ('fpdf.svg',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\svg.py',
   'PYMODULE'),
  ('fpdf.syntax',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\syntax.py',
   'PYMODULE'),
  ('fpdf.table',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\table.py',
   'PYMODULE'),
  ('fpdf.template',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\template.py',
   'PYMODULE'),
  ('fpdf.text_region',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\text_region.py',
   'PYMODULE'),
  ('fpdf.transitions',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\transitions.py',
   'PYMODULE'),
  ('fpdf.unicode_script',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\unicode_script.py',
   'PYMODULE'),
  ('fpdf.util',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\util.py',
   'PYMODULE'),
  ('fractions', 'c:\\program files\\python38\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'c:\\program files\\python38\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'c:\\program files\\python38\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'c:\\program files\\python38\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python38\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'c:\\program files\\python38\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python38\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python38\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'c:\\program files\\python38\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'c:\\program files\\python38\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\program files\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'c:\\program files\\python38\\lib\\html\\parser.py',
   'PYMODULE'),
  ('http', 'c:\\program files\\python38\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'c:\\program files\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\program files\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'c:\\program files\\python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('imageio',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\__init__.py',
   'PYMODULE'),
  ('imageio.config',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\config\\__init__.py',
   'PYMODULE'),
  ('imageio.config.extensions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\config\\extensions.py',
   'PYMODULE'),
  ('imageio.config.plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\config\\plugins.py',
   'PYMODULE'),
  ('imageio.core',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\__init__.py',
   'PYMODULE'),
  ('imageio.core.fetching',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\fetching.py',
   'PYMODULE'),
  ('imageio.core.findlib',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\findlib.py',
   'PYMODULE'),
  ('imageio.core.format',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\format.py',
   'PYMODULE'),
  ('imageio.core.imopen',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\imopen.py',
   'PYMODULE'),
  ('imageio.core.legacy_plugin_wrapper',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\legacy_plugin_wrapper.py',
   'PYMODULE'),
  ('imageio.core.request',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\request.py',
   'PYMODULE'),
  ('imageio.core.util',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\util.py',
   'PYMODULE'),
  ('imageio.core.v3_plugin_api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\v3_plugin_api.py',
   'PYMODULE'),
  ('imageio.plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\__init__.py',
   'PYMODULE'),
  ('imageio.plugins._bsdf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_bsdf.py',
   'PYMODULE'),
  ('imageio.plugins._dicom',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_dicom.py',
   'PYMODULE'),
  ('imageio.plugins._freeimage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_freeimage.py',
   'PYMODULE'),
  ('imageio.plugins._swf',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\_swf.py',
   'PYMODULE'),
  ('imageio.plugins._tifffile',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.bsdf',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\bsdf.py',
   'PYMODULE'),
  ('imageio.plugins.dicom',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\dicom.py',
   'PYMODULE'),
  ('imageio.plugins.example',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\example.py',
   'PYMODULE'),
  ('imageio.plugins.feisem',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\feisem.py',
   'PYMODULE'),
  ('imageio.plugins.ffmpeg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\ffmpeg.py',
   'PYMODULE'),
  ('imageio.plugins.fits',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\fits.py',
   'PYMODULE'),
  ('imageio.plugins.freeimage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\freeimage.py',
   'PYMODULE'),
  ('imageio.plugins.freeimagemulti',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\freeimagemulti.py',
   'PYMODULE'),
  ('imageio.plugins.gdal',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\gdal.py',
   'PYMODULE'),
  ('imageio.plugins.grab',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\grab.py',
   'PYMODULE'),
  ('imageio.plugins.lytro',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\lytro.py',
   'PYMODULE'),
  ('imageio.plugins.npz',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\npz.py',
   'PYMODULE'),
  ('imageio.plugins.opencv',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\opencv.py',
   'PYMODULE'),
  ('imageio.plugins.pillow',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillow.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_info',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillow_info.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_legacy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillow_legacy.py',
   'PYMODULE'),
  ('imageio.plugins.pillowmulti',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillowmulti.py',
   'PYMODULE'),
  ('imageio.plugins.pyav',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\pyav.py',
   'PYMODULE'),
  ('imageio.plugins.rawpy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\rawpy.py',
   'PYMODULE'),
  ('imageio.plugins.simpleitk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\simpleitk.py',
   'PYMODULE'),
  ('imageio.plugins.spe',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\spe.py',
   'PYMODULE'),
  ('imageio.plugins.swf',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\swf.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile_v3',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\tifffile_v3.py',
   'PYMODULE'),
  ('imageio.typing',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\typing.py',
   'PYMODULE'),
  ('imageio.v2',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\v2.py',
   'PYMODULE'),
  ('imageio.v3',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\v3.py',
   'PYMODULE'),
  ('imp', 'c:\\program files\\python38\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'c:\\program files\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'c:\\program files\\python38\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'c:\\program files\\python38\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'c:\\program files\\python38\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('lazy_loader',
   'c:\\program files\\python38\\lib\\site-packages\\lazy_loader\\__init__.py',
   'PYMODULE'),
  ('logging',
   'c:\\program files\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'c:\\program files\\python38\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'c:\\program files\\python38\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'c:\\program files\\python38\\lib\\mimetypes.py', 'PYMODULE'),
  ('mouseinfo',
   'c:\\program files\\python38\\lib\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\program files\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\program files\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\program files\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\program files\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\program files\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\program files\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\program files\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\program files\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\program files\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\program files\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\program files\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\program files\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\program files\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\program files\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\program files\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\program files\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\program files\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\program files\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\program files\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netbios',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc', 'c:\\program files\\python38\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'c:\\program files\\python38\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python38\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'c:\\program files\\python38\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python38\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python38\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'c:\\program files\\python38\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'c:\\program files\\python38\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'c:\\program files\\python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'c:\\program files\\python38\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'c:\\program files\\python38\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python38\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'c:\\program files\\python38\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\program files\\python38\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\program files\\python38\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'c:\\program files\\python38\\lib\\py_compile.py', 'PYMODULE'),
  ('pyautogui',
   'c:\\program files\\python38\\lib\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_osx',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_win',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_x11',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE'),
  ('pydoc', 'c:\\program files\\python38\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'c:\\program files\\python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\program files\\python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygetwindow',
   'c:\\program files\\python38\\lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('pymsgbox',
   'c:\\program files\\python38\\lib\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE'),
  ('pymsgbox._native_win',
   'c:\\program files\\python38\\lib\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE'),
  ('pyperclip',
   'c:\\program files\\python38\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyreadline3',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyrect',
   'c:\\program files\\python38\\lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('pyscreeze',
   'c:\\program files\\python38\\lib\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE'),
  ('pythoncom',
   'c:\\program files\\python38\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pytweening',
   'c:\\program files\\python38\\lib\\site-packages\\pytweening\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'c:\\program files\\python38\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python38\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'c:\\program files\\python38\\lib\\random.py', 'PYMODULE'),
  ('readline',
   'c:\\program files\\python38\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('runpy', 'c:\\program files\\python38\\lib\\runpy.py', 'PYMODULE'),
  ('scipy',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('scipy.__config__',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy._lib',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy._lib._finite_differences',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_finite_differences.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy.constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.fft',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_multimethods',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_fftlog_multimethods.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.integrate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._flinalg_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_flinalg_py.py',
   'PYMODULE'),
  ('scipy.linalg._interpolative_backend',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_interpolative_backend.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.flinalg',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\flinalg.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._highs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_highs\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib.triangulation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\triangulation.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.sparse',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse._arrays',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_arrays.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.spatial',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.special',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special._add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.stats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._boost',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats._rvs_sampling',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_rvs_sampling.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats.statlib',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\statlib.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.version',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('secrets', 'c:\\program files\\python38\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python38\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'c:\\program files\\python38\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python38\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python38\\lib\\signal.py', 'PYMODULE'),
  ('skimage',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\__init__.py',
   'PYMODULE'),
  ('skimage._shared',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\__init__.py',
   'PYMODULE'),
  ('skimage._shared._warnings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\_warnings.py',
   'PYMODULE'),
  ('skimage._shared.filters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\filters.py',
   'PYMODULE'),
  ('skimage._shared.tester',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\tester.py',
   'PYMODULE'),
  ('skimage._shared.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\utils.py',
   'PYMODULE'),
  ('skimage._shared.version_requirements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\version_requirements.py',
   'PYMODULE'),
  ('skimage.color',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\__init__.py',
   'PYMODULE'),
  ('skimage.color.adapt_rgb',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\adapt_rgb.py',
   'PYMODULE'),
  ('skimage.color.colorconv',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\colorconv.py',
   'PYMODULE'),
  ('skimage.color.colorlabel',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\colorlabel.py',
   'PYMODULE'),
  ('skimage.color.delta_e',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\delta_e.py',
   'PYMODULE'),
  ('skimage.color.rgb_colors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\rgb_colors.py',
   'PYMODULE'),
  ('skimage.data',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\__init__.py',
   'PYMODULE'),
  ('skimage.data._binary_blobs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\_binary_blobs.py',
   'PYMODULE'),
  ('skimage.data._fetchers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\_fetchers.py',
   'PYMODULE'),
  ('skimage.data._registry',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\_registry.py',
   'PYMODULE'),
  ('skimage.exposure',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\__init__.py',
   'PYMODULE'),
  ('skimage.exposure._adapthist',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\_adapthist.py',
   'PYMODULE'),
  ('skimage.exposure.exposure',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\exposure.py',
   'PYMODULE'),
  ('skimage.exposure.histogram_matching',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\histogram_matching.py',
   'PYMODULE'),
  ('skimage.io',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\__init__.py',
   'PYMODULE'),
  ('skimage.io._image_stack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_image_stack.py',
   'PYMODULE'),
  ('skimage.io._io',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\_io.py',
   'PYMODULE'),
  ('skimage.io._plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\__init__.py',
   'PYMODULE'),
  ('skimage.io._plugins.fits_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\fits_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.gdal_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\gdal_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.imageio_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\imageio_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.imread_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\imread_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.matplotlib_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\matplotlib_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.pil_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\pil_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.simpleitk_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\simpleitk_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.tifffile_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\tifffile_plugin.py',
   'PYMODULE'),
  ('skimage.io.collection',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\collection.py',
   'PYMODULE'),
  ('skimage.io.manage_plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\manage_plugins.py',
   'PYMODULE'),
  ('skimage.io.sift',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\sift.py',
   'PYMODULE'),
  ('skimage.io.util',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\util.py',
   'PYMODULE'),
  ('skimage.metrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\__init__.py',
   'PYMODULE'),
  ('skimage.metrics._adapted_rand_error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_adapted_rand_error.py',
   'PYMODULE'),
  ('skimage.metrics._contingency_table',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_contingency_table.py',
   'PYMODULE'),
  ('skimage.metrics._structural_similarity',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_structural_similarity.py',
   'PYMODULE'),
  ('skimage.metrics._variation_of_information',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_variation_of_information.py',
   'PYMODULE'),
  ('skimage.metrics.set_metrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\set_metrics.py',
   'PYMODULE'),
  ('skimage.metrics.simple_metrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\simple_metrics.py',
   'PYMODULE'),
  ('skimage.util',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\__init__.py',
   'PYMODULE'),
  ('skimage.util._invert',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\_invert.py',
   'PYMODULE'),
  ('skimage.util._label',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\_label.py',
   'PYMODULE'),
  ('skimage.util._map_array',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_map_array.py',
   'PYMODULE'),
  ('skimage.util._montage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_montage.py',
   'PYMODULE'),
  ('skimage.util._regular_grid',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_regular_grid.py',
   'PYMODULE'),
  ('skimage.util._slice_along_axes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_slice_along_axes.py',
   'PYMODULE'),
  ('skimage.util.apply_parallel',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\apply_parallel.py',
   'PYMODULE'),
  ('skimage.util.arraycrop',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\arraycrop.py',
   'PYMODULE'),
  ('skimage.util.compare',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\compare.py',
   'PYMODULE'),
  ('skimage.util.dtype',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\dtype.py',
   'PYMODULE'),
  ('skimage.util.lookfor',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\lookfor.py',
   'PYMODULE'),
  ('skimage.util.noise',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\noise.py',
   'PYMODULE'),
  ('skimage.util.shape',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\shape.py',
   'PYMODULE'),
  ('skimage.util.unique',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\unique.py',
   'PYMODULE'),
  ('smtplib', 'c:\\program files\\python38\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'c:\\program files\\python38\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'c:\\program files\\python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('ssl', 'c:\\program files\\python38\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'c:\\program files\\python38\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python38\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'c:\\program files\\python38\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'c:\\program files\\python38\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'c:\\program files\\python38\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'c:\\program files\\python38\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python38\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'c:\\program files\\python38\\lib\\threading.py', 'PYMODULE'),
  ('tifffile',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\__init__.py',
   'PYMODULE'),
  ('tifffile._imagecodecs',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\_imagecodecs.py',
   'PYMODULE'),
  ('tifffile.geodb',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\geodb.py',
   'PYMODULE'),
  ('tifffile.tifffile',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\tifffile.py',
   'PYMODULE'),
  ('timeit', 'c:\\program files\\python38\\lib\\timeit.py', 'PYMODULE'),
  ('tkinter',
   'c:\\program files\\python38\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\program files\\python38\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\program files\\python38\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\program files\\python38\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\program files\\python38\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\program files\\python38\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'c:\\program files\\python38\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\program files\\python38\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'c:\\program files\\python38\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'c:\\program files\\python38\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'c:\\program files\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'c:\\program files\\python38\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'c:\\program files\\python38\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'c:\\program files\\python38\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'c:\\program files\\python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\program files\\python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\program files\\python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\program files\\python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\program files\\python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'c:\\program files\\python38\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\program files\\python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\program files\\python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\program files\\python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\program files\\python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\program files\\python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'c:\\program files\\python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\program files\\python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\program files\\python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\program files\\python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'c:\\program files\\python38\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'c:\\program files\\python38\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'c:\\program files\\python38\\lib\\webbrowser.py', 'PYMODULE'),
  ('win32com',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'c:\\program files\\python38\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom',
   'c:\\program files\\python38\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\program files\\python38\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\program files\\python38\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\program files\\python38\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\program files\\python38\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\program files\\python38\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\program files\\python38\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\program files\\python38\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\program files\\python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\program files\\python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\program files\\python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\program files\\python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\program files\\python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\program files\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\program files\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\program files\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\program files\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\program files\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\program files\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\program files\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\program files\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\program files\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\program files\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'c:\\program files\\python38\\lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('zipfile', 'c:\\program files\\python38\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'c:\\program files\\python38\\lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.compat',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])

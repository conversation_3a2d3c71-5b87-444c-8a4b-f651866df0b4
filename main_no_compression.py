import os
import time
import cv2
import numpy as np
import pyautogui
from PIL import Image, ImageDraw, ImageFont, ImageTk
from fpdf import FPDF
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from io import BytesIO
import tkinter.simpledialog as simpledialog


# 全局内置账号及密码
ACCOUNTS = {
    "管理员": "123456",
    "办公室": "123456",
    "会议室": "123456",
    "覃瀚华": "123456",
    "方俞惠": "123456",
    "梁立斌": "123456",
    "邹昌光": "123456",
    "师文清": "123456"
}


class AppConfig:
    """应用程序配置参数"""
    # 监控设置
    MONITOR_INTERVAL = 1  # 截屏检测间隔(秒)
    SIMILARITY_THRESHOLD = 0.95  # 画面变化阈值

    # 水印设置
    WATERMARK_TEXT = "内部资料，禁止外传"
    WATERMARK_FONT_SIZE = 20
    WATERMARK_COLOR = (0, 0, 0, 32)  # RGBA
    WATERMARK_SPACING = 200  # 水印间距(像素)
    WATERMARK_ANGLE = 30  # 旋转角度
    WATERMARK_ENABLED = True  # 是否启用水印

    # PDF设置
    PDF_PAGE_RATIO = 16 / 9  # 页面宽高比
    PDF_PAGE_HEIGHT_PT = 400  # 页面高度(点)
    PDF_IMAGE_SCALE = 0.9  # 图片缩放比例
    PDF_DEFAULT_PREFIX = "PPT截图"  # 默认文件名前缀

    # 界面设置
    STATUS_FONT = ('Helvetica', 10, 'bold')
    STATUS_COLORS = {
        'running': '#51cf66',  # 运行中(绿色)
        'paused': '#fcc419',   # 已暂停(黄色)
        'stopped': '#ff6b6b'  # 已停止(红色)
    }

    # 预览设置
    PREVIEW_THUMB_SIZE = (200, 150)  # 缩略图尺寸
    PREVIEW_COLS = 4  # 每行列数
    FULL_PREVIEW_SCALE = 0.8  # 完整预览窗口占屏幕比例


class LoginDialog(simpledialog.Dialog):
    def __init__(self, parent, title=None):
        super().__init__(parent, title=title)

    def body(self, master):
        tk.Label(master, text="用户名:").grid(row=0, column=0, sticky="e", padx=5, pady=5)
        tk.Label(master, text="密码:").grid(row=1, column=0, sticky="e", padx=5, pady=5)
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.username_entry = tk.Entry(master, textvariable=self.username_var)
        self.password_entry = tk.Entry(master, textvariable=self.password_var, show="*")
        self.username_entry.grid(row=0, column=1, padx=5, pady=5)
        self.password_entry.grid(row=1, column=1, padx=5, pady=5)
        return self.username_entry

    def apply(self):
        self.result = (self.username_var.get(), self.password_var.get())

    def validate(self):
        username, password = self.username_var.get(), self.password_var.get()
        if username in ACCOUNTS and ACCOUNTS[username] == password:
            return True
        else:
            messagebox.showerror("登录失败", "用户名或密码错误")
            return False

    def show(self):
        # 强制窗口更新，以便正确获取屏幕尺寸
        self.update()

        # 获取屏幕尺寸
        screen_width = self.parent.winfo_screenwidth()
        screen_height = self.parent.winfo_screenheight()

        # 计算窗口位置
        window_width = 300  # 窗口宽度
        window_height = 200  # 窗口高度
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.resizable(False, False)
        self.transient(self.parent)
        self.grab_set()
        self.focus_set()
        self.wait_window()


class PPTMonitor:
    def __init__(self):
        self.is_monitoring = False
        self.is_paused = False
        self.screenshots = []
        self.last_screenshot = None
        self.watermark_text = AppConfig.WATERMARK_TEXT
        self.similarity_threshold = AppConfig.SIMILARITY_THRESHOLD
        self.gui = None  # GUI实例

    def start_monitoring(self):
        self.is_monitoring = True
        self.is_paused = False
        self.screenshots = []
        print("开始监控屏幕变化...")

        while self.is_monitoring:
            if not self.is_paused:
                screenshot = pyautogui.screenshot()
                screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

                if self.last_screenshot is None or not self.is_similar(self.last_screenshot, screenshot_cv):
                    watermarked = self.add_watermark(screenshot)
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    self.screenshots.append(watermarked)
                    print(f"检测到画面变化，已截屏 ({len(self.screenshots)})")
                    self.last_screenshot = screenshot_cv

            time.sleep(AppConfig.MONITOR_INTERVAL)

    def pause_monitoring(self):
        self.is_paused = True
        print("监控已暂停")

    def resume_monitoring(self):
        self.is_paused = False
        print("监控已恢复")

    def stop_monitoring(self):
        self.is_monitoring = False
        print("监控已停止")

    def is_similar(self, img1, img2):
        """计算两张图片的相似度"""
        if img1.shape != img2.shape:
            return False

        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        try:
            from skimage.metrics import structural_similarity as ssim
            score = ssim(gray1, gray2)
        except ImportError:
            try:
                score = cv2.quality.QualitySSIM_compute(gray1, gray2)[0]
            except:
                diff = cv2.absdiff(gray1, gray2)
                _, diff = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)
                score = 1.0 - (np.count_nonzero(diff) / float(diff.size))

        return score > self.similarity_threshold

    def add_watermark(self, image):
        """添加全屏水印"""
        if not AppConfig.WATERMARK_ENABLED:
            return image

        img = image.copy()
        draw = ImageDraw.Draw(img, 'RGBA')

        try:
            font = ImageFont.truetype("simhei.ttf", AppConfig.WATERMARK_FONT_SIZE)  # 中等大小字体
        except:
            font = ImageFont.load_default()

        width, height = img.size

        # 获取当前用户名和日期时间
        username = self.gui.current_user  # 从GUI获取用户名
        now = time.strftime("%Y-%m-%d %H:%M:%S")

        # 组合水印文本
        watermark_text1 = self.watermark_text
        watermark_text2 = f"{username} - {now}"

        bbox1 = draw.textbbox((0, 0), watermark_text1, font=font)
        text_width1 = bbox1[2] - bbox1[0]
        text_height1 = bbox1[3] - bbox1[1]

        bbox2 = draw.textbbox((0, 0), watermark_text2, font=font)
        text_width2 = bbox2[2] - bbox2[0]
        text_height2 = bbox2[3] - bbox2[1]

        angle = AppConfig.WATERMARK_ANGLE  # 旋转角度
        fill_color = AppConfig.WATERMARK_COLOR

        # 增加行间距
        line_spacing = 1

        for x in range(0, width, max(text_width1, text_width2) + AppConfig.WATERMARK_SPACING):
            for y in range(0, height, text_height1 + text_height2 + line_spacing + AppConfig.WATERMARK_SPACING):
                # 创建水印文本图像
                txt_img = Image.new('RGBA', (max(text_width1, text_width2), text_height1 + text_height2 + line_spacing + 5), (255, 255, 255, 0))
                txt_draw = ImageDraw.Draw(txt_img)

                # 计算居中位置
                x1 = (max(text_width1, text_width2) - text_width1) / 2
                x2 = (max(text_width1, text_width2) - text_width2) / 2

                # 绘制水印文本
                txt_draw.text((x1, 0), watermark_text1, font=font, fill=fill_color)
                txt_draw.text((x2, text_height1 + line_spacing), watermark_text2, font=font, fill=fill_color)

                # 兼容不同Pillow版本的旋转方法
                try:
                    # 新版本Pillow
                    rotated = txt_img.rotate(angle, expand=1, resample=Image.Resampling.BICUBIC)
                except AttributeError:
                    # 旧版本Pillow
                    rotated = txt_img.rotate(angle, expand=1, resample=Image.BICUBIC)

                img.paste(rotated, (x, y), rotated)

        return img

    def save_selected_as_pdf(self, selected_indices, output_path):
        """将截图保存为16:9比例的PDF，图片90%缩放居中，无压缩"""
        # 设置16:9页面尺寸（以点为单位，假设高度为400pt）
        page_height = AppConfig.PDF_PAGE_HEIGHT_PT  # 可以调整这个值改变PDF大小
        page_width = page_height * AppConfig.PDF_PAGE_RATIO
        pdf = FPDF(unit="pt", format=(page_width, page_height))

        for idx in selected_indices:
            if 0 <= idx < len(self.screenshots):
                # 使用内存流处理图片，不进行压缩
                with BytesIO() as img_bytes:
                    # 保存为PNG格式，不进行压缩
                    self.screenshots[idx].save(img_bytes, format='PNG')
                    img_bytes.seek(0)

                    # 获取图片原始尺寸和比例
                    with Image.open(img_bytes) as img:
                        img_width, img_height = img.size
                        img_ratio = img_width / img_height

                    # 计算90%缩放后的尺寸（基于页面短边）
                    max_display_width = page_width * AppConfig.PDF_IMAGE_SCALE
                    max_display_height = page_height * AppConfig.PDF_IMAGE_SCALE

                    # 保持原图比例计算显示尺寸
                    if img_ratio > (16 / 9):  # 比16:9更宽的图片
                        display_width = min(max_display_width, img_width * AppConfig.PDF_IMAGE_SCALE * 72 / 96)
                        display_height = display_width / img_ratio
                    else:  # 比16:9更高的图片
                        display_height = min(max_display_height, img_height * AppConfig.PDF_IMAGE_SCALE * 72 / 96)
                        display_width = display_height * img_ratio

                    # 计算居中位置
                    x = (page_width - display_width) / 2
                    y = (page_height - display_height) / 2

                    # 添加到PDF
                    pdf.add_page()
                    img_bytes.seek(0)
                    pdf.image(img_bytes, x, y, display_width, display_height)

        pdf.output(output_path, "F")
        print(f"16:9比例PDF已保存到: {output_path}")


class PPTMonitorGUI:
    def __init__(self, master, current_user):
        self.master = master
        self.current_user = current_user  # 当前登录的用户
        self.monitor = PPTMonitor()
        self.monitor.gui = self  # 将GUI实例传递给PPTMonitor

        # 初始化设置变量
        self.interval_var = tk.StringVar(value=str(AppConfig.MONITOR_INTERVAL))
        self.threshold_var = tk.StringVar(value=str(AppConfig.SIMILARITY_THRESHOLD))
        self.watermark_enabled_var = tk.BooleanVar(value=AppConfig.WATERMARK_ENABLED)  # 水印开关

        master.title("PPT截屏监控工具 - 无压缩版本")

        # 强制窗口更新，以便正确获取屏幕尺寸
        master.update()

        # 获取屏幕尺寸
        screen_width = master.winfo_screenwidth()
        screen_height = master.winfo_screenheight()

        # 计算窗口位置
        window_width = 400  # 窗口宽度
        window_height = 400  # 窗口高度
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置
        master.geometry(f"{window_width}x{window_height}+{x}+{y}")  # 减小窗口尺寸，因为移除了设置区域

        # 设置窗口图标（如果有）
        try:
            # 尝试使用Pillow生成简单图标
            icon = Image.new('RGBA', (64, 64), (255, 255, 255, 0))
            draw = ImageDraw.Draw(icon)
            draw.rectangle([5, 5, 59, 59], outline="#3498db", width=2)
            draw.rectangle([15, 15, 49, 49], fill="#3498db")

            # 转换为PhotoImage
            icon_bytes = BytesIO()
            icon.save(icon_bytes, format='PNG')
            icon_bytes.seek(0)
            icon_image = ImageTk.PhotoImage(data=icon_bytes.read())
            master.iconphoto(True, icon_image)
        except Exception:
            # 忽略图标设置错误
            pass

        # 自定义颜色样式
        self.style = ttk.Style()
        self.style.configure("Red.TLabel", foreground=AppConfig.STATUS_COLORS['stopped'], font=AppConfig.STATUS_FONT)
        self.style.configure("Green.TLabel", foreground=AppConfig.STATUS_COLORS['running'], font=AppConfig.STATUS_FONT)
        self.style.configure("Yellow.TLabel", foreground=AppConfig.STATUS_COLORS['paused'], font=AppConfig.STATUS_FONT)

        # 创建菜单栏
        self.create_menu()

        # 创建UI
        self.setup_ui()

    def create_menu(self):
        """创建顶部菜单栏"""
        menubar = tk.Menu(self.master)
        self.master.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="退出", command=self.master.quit)

        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="监控参数设置", command=self.show_settings)
        settings_menu.add_command(label="修改密码", command=self.change_password)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)

    def change_password(self):
        """修改当前用户密码"""
        cp_win = tk.Toplevel(self.master)
        cp_win.title("修改密码")

        # 获取屏幕尺寸
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()

        # 计算窗口位置
        window_width = 300  # 窗口宽度
        window_height = 300  # 窗口高度
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置
        cp_win.geometry(f"{window_width}x{window_height}+{x}+{y}")
        cp_win.resizable(False, False)
        cp_win.transient(self.master)
        cp_win.grab_set()

        ttk.Label(cp_win, text="旧密码:").pack(pady=5)
        old_pass = ttk.Entry(cp_win, show="*")
        old_pass.pack(pady=5)

        ttk.Label(cp_win, text="新密码:").pack(pady=5)
        new_pass = ttk.Entry(cp_win, show="*")
        new_pass.pack(pady=5)

        ttk.Label(cp_win, text="确认新密码:").pack(pady=5)
        confirm_pass = ttk.Entry(cp_win, show="*")
        confirm_pass.pack(pady=5)

        def submit():
            op = old_pass.get()
            npw = new_pass.get()
            cpw = confirm_pass.get()
            if ACCOUNTS.get(self.current_user) != op:
                messagebox.showerror("错误", "旧密码不正确")
                return
            if npw != cpw:
                messagebox.showerror("错误", "新密码与确认密码不匹配")
                return
            ACCOUNTS[self.current_user] = npw
            messagebox.showinfo("成功", "密码修改成功")
            cp_win.destroy()

        ttk.Button(cp_win, text="确认", command=submit).pack(pady=10)

    def show_settings(self):
        """显示设置对话框"""
        # 确保变量值为最新值
        self.interval_var.set(str(AppConfig.MONITOR_INTERVAL))
        self.threshold_var.set(str(AppConfig.SIMILARITY_THRESHOLD))

        settings_win = tk.Toplevel(self.master)
        settings_win.title("监控参数设置")

        # 获取屏幕尺寸
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()

        # 计算窗口位置
        window_width = 300  # 窗口宽度
        window_height = 250  # 窗口高度
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置
        settings_win.geometry(f"{window_width}x{window_height}+{x}+{y}")
        settings_win.resizable(False, False)
        settings_win.transient(self.master)  # 设置为主窗口的子窗口
        settings_win.grab_set()  # 模态窗口

        # 创建设置框架
        settings_frame = ttk.Frame(settings_win, padding=15)
        settings_frame.pack(fill="both", expand=True)

        # 间隔设置
        ttk.Label(settings_frame, text="检测间隔 (秒):").grid(row=0, column=0, sticky="w", pady=10)
        ttk.Spinbox(
            settings_frame,
            from_=0.5,
            to=5.0,
            increment=0.5,
            textvariable=self.interval_var,
            width=8
        ).grid(row=0, column=1, sticky="w", pady=10, padx=5)

        # 相似度阈值设置
        ttk.Label(settings_frame, text="变化阈值 (0.8-0.99):").grid(row=1, column=0, sticky="w", pady=10)
        ttk.Spinbox(
            settings_frame,
            from_=0.80,
            to=0.99,
            increment=0.01,
            textvariable=self.threshold_var,
            width=8
        ).grid(row=1, column=1, sticky="w", pady=10, padx=5)

        # 水印开关
        watermark_chk = ttk.Checkbutton(
            settings_frame,
            text="启用水印",
            variable=self.watermark_enabled_var,
            command=self.check_watermark_password
        )
        watermark_chk.grid(row=2, column=0, columnspan=2, sticky="w", pady=10)

        # 按钮区域
        btn_frame = ttk.Frame(settings_frame)
        btn_frame.grid(row=3, column=0, columnspan=2, pady=(20, 0))

        ttk.Button(
            btn_frame,
            text="应用",
            command=lambda: [self.apply_settings(), settings_win.destroy()]
        ).pack(side="left", padx=5)

        ttk.Button(
            btn_frame,
            text="取消",
            command=settings_win.destroy
        ).pack(side="left", padx=5)

    def check_watermark_password(self):
        """检查水印密码"""
        if self.watermark_enabled_var.get():
            # 启用水印，不需要密码
            return

        # 取消水印，需要验证密码
        password = simpledialog.askstring("密码验证", "请输入超级密码：", show='*')
        if password == 'Huagev587@':
            # 密码正确，允许取消水印
            return
        else:
            # 密码错误，恢复勾选状态
            messagebox.showerror("错误", "超级密码错误！")
            self.watermark_enabled_var.set(True)  # 恢复勾选
            return

    def show_about(self):
        """显示关于对话框"""
        about_win = tk.Toplevel(self.master)
        about_win.title("关于")

        # 获取屏幕尺寸
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()

        # 计算窗口位置
        window_width = 300  # 窗口宽度
        window_height = 200  # 窗口高度
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置
        about_win.geometry(f"{window_width}x{window_height}+{x}+{y}")
        about_win.resizable(False, False)
        about_win.transient(self.master)  # 设置为主窗口的子窗口

        # 创建关于内容
        about_frame = ttk.Frame(about_win, padding=15)
        about_frame.pack(fill="both", expand=True)

        ttk.Label(
            about_frame,
            text="PPT截屏监控工具 - 无压缩版本",
            font=('微软雅黑', 14, 'bold')
        ).pack(pady=(0, 10))

        ttk.Label(
            about_frame,
            text="版本: 1.5 (无压缩)\n\n© 2025 覃瀚华\n\n自动监控PPT演示文稿变化并截图\n保存时不进行图片和PDF压缩",
            justify="center"
        ).pack(fill="both", expand=True)

        ttk.Button(
            about_frame,
            text="确定",
            command=about_win.destroy
        ).pack(pady=(10, 0))

    def setup_ui(self):
        # 使用Frame组织界面布局
        main_frame = ttk.Frame(self.master)
        main_frame.pack(fill="both", expand=True, padx=15, pady=15)

        # 标题标签
        title_label = ttk.Label(
            main_frame,
            text="PPT截屏监控工具 - 无压缩版本",
            font=('微软雅黑', 14, 'bold')
        )
        title_label.pack(pady=(0, 15))

        # 控制区域Frame
        control_frame = ttk.LabelFrame(main_frame, text="控制面板")
        control_frame.pack(fill="x", pady=5)

        # 监控按钮和暂停按钮放在一行
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill="x", pady=10, padx=10)

        self.monitor_btn = ttk.Button(
            btn_frame,
            text="开始监控",
            command=self.toggle_monitoring,
            width=15
        )
        self.monitor_btn.pack(side="left", padx=(0, 10))

        self.pause_btn = ttk.Button(
            btn_frame,
            text="暂停监控",
            command=self.toggle_pause,
            state=tk.DISABLED,
            width=15
        )
        self.pause_btn.pack(side="left")

        # 状态区域Frame
        status_frame = ttk.LabelFrame(main_frame, text="当前状态")
        status_frame.pack(fill="x", pady=10)

        # 状态信息
        status_info_frame = ttk.Frame(status_frame)
        status_info_frame.pack(fill="x", pady=10, padx=10)

        # 状态标签
        status_label_text = ttk.Label(status_info_frame, text="状态:")
        status_label_text.grid(row=0, column=0, sticky="w", pady=3)

        self.status_label = ttk.Label(status_info_frame, text="未监控", style="Red.TLabel")
        self.status_label.grid(row=0, column=1, sticky="w", pady=3, padx=5)

        # 截图计数
        count_label_text = ttk.Label(status_info_frame, text="已截屏:")
        count_label_text.grid(row=1, column=0, sticky="w", pady=3)

        self.count_label = ttk.Label(status_info_frame, text="0")
        self.count_label.grid(row=1, column=1, sticky="w", pady=3, padx=5)

        # 预览按钮
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill="x", pady=10)

        self.preview_btn = ttk.Button(
            action_frame,
            text="结束并预览截图",
            command=self.preview_screenshots,
            state=tk.DISABLED,
            width=20
        )
        self.preview_btn.pack(pady=5)

        # 在右下角添加版权信息
        copyright_frame = ttk.Frame(self.master)
        copyright_frame.pack(side="bottom", fill="x", pady=5)

        ttk.Label(
            copyright_frame,
            text="© 2025 覃瀚华 | 版本: 1.5 (无压缩)",
            font=('微软雅黑', 10),
            foreground="#666666"
        ).pack(side="right", padx=10)

    def apply_settings(self, silent=False):
        """应用用户设置的参数"""
        try:
            # 获取并验证间隔设置
            interval = float(self.interval_var.get())
            if 0.1 <= interval <= 10:
                AppConfig.MONITOR_INTERVAL = interval

            # 获取并验证相似度阈值
            threshold = float(self.threshold_var.get())
            if 0.5 <= threshold <= 0.99:
                AppConfig.SIMILARITY_THRESHOLD = threshold
                if hasattr(self.monitor, 'similarity_threshold'):
                    self.monitor.similarity_threshold = threshold

            # 获取水印开关状态
            old_watermark_enabled = AppConfig.WATERMARK_ENABLED
            new_watermark_enabled = self.watermark_enabled_var.get()

            # 只有当水印开关状态改变时，才进行密码验证
            if old_watermark_enabled != new_watermark_enabled:
                AppConfig.WATERMARK_ENABLED = new_watermark_enabled

            # 只在非静默模式下显示消息
            if not silent:
                messagebox.showinfo("设置", "设置已应用")
            return True
        except ValueError:
            if not silent:
                messagebox.showerror("错误", "请输入有效的数值")
            return False

    def toggle_monitoring(self):
        if self.monitor.is_monitoring:
            self.monitor.stop_monitoring()
            self.monitor_btn.config(text="开始监控")
            self.pause_btn.config(state=tk.DISABLED, text="暂停监控")
            self.status_label.config(text="未监控", style="Red.TLabel")
            self.preview_btn.config(state=tk.NORMAL)
        else:
            # 应用当前设置（静默模式）
            if not self.apply_settings(silent=True):
                # 如果设置应用失败，显示错误并返回
                messagebox.showerror("错误", "请先确保所有设置有效")
                return

            monitor_thread = threading.Thread(target=self.monitor.start_monitoring)
            monitor_thread.daemon = True
            monitor_thread.start()

            self.monitor_btn.config(text="停止监控")
            self.pause_btn.config(state=tk.NORMAL)
            self.status_label.config(text="监控中...", style="Green.TLabel")
            self.preview_btn.config(state=tk.DISABLED)
            self.update_count()

    def toggle_pause(self):
        if self.monitor.is_paused:
            self.monitor.resume_monitoring()
            self.pause_btn.config(text="暂停监控")
            self.status_label.config(text="监控中...", style="Green.TLabel")
        else:
            self.monitor.pause_monitoring()
            self.pause_btn.config(text="恢复监控")
            self.status_label.config(text="已暂停", style="Yellow.TLabel")

    def update_count(self):
        if self.monitor.is_monitoring:
            self.count_label.config(text=f"{len(self.monitor.screenshots)}")
            self.master.after(1000, self.update_count)

    def preview_screenshots(self):
        if not self.monitor.screenshots:
            messagebox.showinfo("提示", "没有可预览的截图")
            return

        preview_win = tk.Toplevel(self.master)
        preview_win.title("截图预览与导出 - 无压缩版本")

        # 获取屏幕尺寸
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()

        # 计算窗口位置
        window_width = 1000  # 窗口宽度
        window_height = 700  # 窗口高度
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置
        preview_win.geometry(f"{window_width}x{window_height}+{x}+{y}")
        preview_win.minsize(800, 600)  # 设置最小窗口大小

        # 创建主框架并添加边距
        main_frame = ttk.Frame(preview_win, padding=10)
        main_frame.pack(fill="both", expand=True)

        # 顶部信息区域
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill="x", pady=(0, 10))

        ttk.Label(
            info_frame,
            text=f"共 {len(self.monitor.screenshots)} 张截图，双击可查看大图 (无压缩版本)",
            font=('微软雅黑', 12)
        ).pack(side="left")

        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill="x", pady=(0, 10))

        select_frame = ttk.LabelFrame(control_frame, text="选择操作")
        select_frame.pack(side="left", padx=(0, 20))

        ttk.Button(
            select_frame,
            text="全选",
            width=10,
            command=lambda: [var.set(True) for var in self.selected]
        ).pack(side="left", padx=5, pady=5)

        ttk.Button(
            select_frame,
            text="全不选",
            width=10,
            command=lambda: [var.set(False) for var in self.selected]
        ).pack(side="left", padx=5, pady=5)

        ttk.Button(
            select_frame,
            text="反选",
            width=10,
            command=lambda: [var.set(not var.get()) for var in self.selected]
        ).pack(side="left", padx=5, pady=5)

        # 画布和滚动条
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(fill="both", expand=True)

        canvas = tk.Canvas(canvas_frame, highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 存储状态和图片引用
        self.selected = [tk.BooleanVar(value=True) for _ in self.monitor.screenshots]
        self.thumbnails = []

        # 设置每行显示的缩略图数量
        cols = AppConfig.PREVIEW_COLS
        for i, screenshot in enumerate(self.monitor.screenshots):
            row = i // cols
            col = i % cols

            # 创建带边框的缩略图框架
            thumb_frame = ttk.Frame(scrollable_frame, borderwidth=2, relief="solid")
            thumb_frame.grid(row=row, column=col, padx=8, pady=8, sticky="nsew")

            # 创建高质量缩略图
            try:
                thumb = screenshot.resize(AppConfig.PREVIEW_THUMB_SIZE, Image.Resampling.LANCZOS)
            except AttributeError:
                thumb = screenshot.resize(AppConfig.PREVIEW_THUMB_SIZE, Image.LANCZOS)

            thumb_tk = ImageTk.PhotoImage(thumb)
            self.thumbnails.append(thumb_tk)

            # 显示缩略图并添加双击事件
            label = ttk.Label(thumb_frame, image=thumb_tk)
            label.image = thumb_tk
            label.bind("<Double-Button-1>", lambda e, idx=i: self.show_full_image(idx))
            label.pack(pady=(5, 0))

            # 在缩略图下方添加复选框
            info_frame = ttk.Frame(thumb_frame)
            info_frame.pack(fill="x", pady=5, padx=5)

            chk = ttk.Checkbutton(
                info_frame,
                text=f"截图 {i + 1}",
                variable=self.selected[i]
            )
            chk.pack(side="left")

        # 底部操作区域
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(fill="x", pady=(10, 0))

        # 添加左侧提示信息
        ttk.Label(
            bottom_frame,
            text="提示: 保存为PDF时会保持原始比例，无压缩",
            foreground="#666666"
        ).pack(side="left")

        # 右侧操作按钮
        ttk.Button(
            bottom_frame,
            text="保存选中为PDF (无压缩)",
            command=lambda: self.save_selected(preview_win)
        ).pack(side="right")

        ttk.Button(
            bottom_frame,
            text="关闭",
            command=preview_win.destroy
        ).pack(side="right", padx=10)

    def show_full_image(self, index):
        """显示更大的完整截图预览"""
        if 0 <= index < len(self.monitor.screenshots):
            full_win = tk.Toplevel()
            full_win.title(f"截图详情 - {index + 1}/{len(self.monitor.screenshots)} (无压缩版本)")

            # 获取屏幕尺寸
            screen_width = self.master.winfo_screenwidth()
            screen_height = self.master.winfo_screenheight()

            # 计算窗口位置
            window_width = int(screen_width * AppConfig.FULL_PREVIEW_SCALE)  # 窗口宽度
            window_height = int(screen_height * AppConfig.FULL_PREVIEW_SCALE)  # 窗口高度
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            full_win.geometry(f"{window_width}x{window_height}+{x}+{y}")

            # 设置更大的窗口尺寸（占屏幕80%）
            screen_width = full_win.winfo_screenwidth()
            screen_height = full_win.winfo_screenheight()
            window_width = int(screen_width * AppConfig.FULL_PREVIEW_SCALE)
            window_height = int(screen_height * AppConfig.FULL_PREVIEW_SCALE)
            full_win.geometry(f"{window_width}x{window_height}+{int(screen_width * 0.1)}+{int(screen_height * 0.1)}")

            # 创建主容器
            main_container = ttk.Frame(full_win, padding=10)
            main_container.pack(fill="both", expand=True)

            # 添加信息栏
            info_bar = ttk.Frame(main_container)
            info_bar.pack(fill="x", pady=(0, 10))

            # 添加图片信息
            img = self.monitor.screenshots[index]
            info_text = f"截图 {index + 1} | 尺寸: {img.width}×{img.height} 像素 (无压缩)"
            ttk.Label(
                info_bar,
                text=info_text,
                font=('微软雅黑', 11)
            ).pack(side="left")

            # 添加方向键提示
            ttk.Label(
                info_bar,
                text="使用键盘方向键可浏览上/下一张",
                foreground="#666666"
            ).pack(side="right")

            # 添加滚动区域
            scroll_container = ttk.Frame(main_container)
            scroll_container.pack(fill="both", expand=True)

            canvas = tk.Canvas(scroll_container)
            scrollbar_v = ttk.Scrollbar(scroll_container, orient="vertical", command=canvas.yview)
            scrollbar_h = ttk.Scrollbar(scroll_container, orient="horizontal", command=canvas.xview)
            canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

            # 显示区域框架
            img_frame = ttk.Frame(canvas)
            canvas.create_window((0, 0), window=img_frame, anchor="nw")

            # 计算适合窗口的显示尺寸
            img_ratio = img.width / img.height

            if img_ratio > 1:  # 横向图片
                display_width = min(img.width, window_width - 60)
                display_height = int(display_width / img_ratio)
            else:  # 纵向图片
                display_height = min(img.height, window_height - 100)
                display_width = int(display_height * img_ratio)

            # 高质量缩放
            try:
                img_display = img.resize((display_width, display_height), Image.Resampling.LANCZOS)
            except AttributeError:
                img_display = img.resize((display_width, display_height), Image.LANCZOS)

            img_tk = ImageTk.PhotoImage(img_display)

            # 显示图片
            img_label = ttk.Label(img_frame, image=img_tk)
            img_label.image = img_tk
            img_label.pack(padx=5, pady=5)

            # 更新滚动区域
            img_frame.update_idletasks()
            canvas.config(scrollregion=canvas.bbox("all"))

            # 布局
            canvas.grid(row=0, column=0, sticky="nsew")
            scrollbar_v.grid(row=0, column=1, sticky="ns")
            scrollbar_h.grid(row=1, column=0, sticky="ew")

            # 使画布可扩展
            scroll_container.grid_rowconfigure(0, weight=1)
            scroll_container.grid_columnconfigure(0, weight=1)

            # 添加底部导航按钮
            nav_frame = ttk.Frame(main_container)
            nav_frame.pack(fill="x", pady=(10, 0))

            # 添加居中的按钮组
            button_frame = ttk.Frame(nav_frame)
            button_frame.pack()

            if index > 0:
                prev_btn = ttk.Button(
                    button_frame,
                    text="上一张",
                    width=12,
                    command=lambda: [full_win.destroy(), self.show_full_image(index - 1)]
                )
                prev_btn.pack(side="left", padx=5)

                # 快捷键
                full_win.bind("<Left>", lambda e: [full_win.destroy(), self.show_full_image(index - 1)])

            close_btn = ttk.Button(
                button_frame,
                text="关闭预览",
                width=12,
                command=full_win.destroy
            )
            close_btn.pack(side="left", padx=5)

            # Esc快捷键关闭
            full_win.bind("<Escape>", lambda e: full_win.destroy())

            if index < len(self.monitor.screenshots) - 1:
                next_btn = ttk.Button(
                    button_frame,
                    text="下一张",
                    width=12,
                    command=lambda: [full_win.destroy(), self.show_full_image(index + 1)]
                )
                next_btn.pack(side="left", padx=5)
                # 快捷键
                full_win.bind("<Right>", lambda e: [full_win.destroy(), self.show_full_image(index + 1)])

    def save_selected(self, preview_win):
        selected_indices = [i for i, var in enumerate(self.selected) if var.get()]
        if not selected_indices:
            messagebox.showwarning("警告", "请至少选择一张截图")
            return

        def save_in_thread():
            # 在后台线程中执行保存操作
            default_filename = AppConfig.PDF_DEFAULT_PREFIX + time.strftime("%y%m%d%H%M") + "_无压缩.pdf"
            # 生成默认文件名
            output_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                initialfile=default_filename,
                filetypes=[("PDF文件", "*.pdf")]
            )
            # 获取用户选择的保存路径
            if output_path:
                try:
                    # 显示进度窗口
                    progress_win = tk.Toplevel()
                    progress_win.title("正在生成PDF (无压缩)")

                    # 获取屏幕尺寸
                    screen_width = self.master.winfo_screenwidth()
                    screen_height = self.master.winfo_screenheight()

                    # 计算窗口位置
                    window_width = 300  # 窗口宽度
                    window_height = 100  # 窗口高度
                    x = (screen_width - window_width) // 2
                    y = (screen_height - window_height) // 2

                    # 设置窗口位置
                    progress_win.geometry(f"{window_width}x{window_height}+{x}+{y}")
                    ttk.Label(progress_win, text="正在生成PDF (无压缩)，请稍候...").pack(pady=10)
                    progress = ttk.Progressbar(progress_win, mode='indeterminate')
                    progress.pack(fill="x", padx=20)
                    progress.start()
                    progress_win.grab_set()  # 模态窗口

                    # 使用内存优化方式保存，不进行压缩
                    start_time = time.time()
                    pdf = FPDF(unit="pt")
                    # 不启用压缩
                    # pdf.set_compression(True)  # 注释掉压缩设置

                    # 预收集所有需要保存的图片
                    images = []
                    for idx in selected_indices:
                        if 0 <= idx < len(self.monitor.screenshots):
                            with BytesIO() as img_bytes:
                                # 保存为PNG格式，不进行压缩
                                self.monitor.screenshots[idx].save(img_bytes, format='PNG')
                                images.append(img_bytes.getvalue())

                    # 批量添加到PDF
                    for img_data in images:
                        with BytesIO(img_data) as img_bytes:
                            # 获取图片尺寸
                            with Image.open(img_bytes) as img:
                                width_pt = img.width * 72 / 96
                                height_pt = img.height * 72 / 96
                            # 添加页面
                            pdf.add_page(format=(width_pt, height_pt))
                            img_bytes.seek(0)
                            pdf.image(img_bytes, 0, 0, width_pt, height_pt)

                    # 保存PDF
                    pdf.output(output_path, "F")
                    # 计算耗时
                    cost_time = time.time() - start_time
                    print(f"PDF生成完成 (无压缩)，耗时{cost_time:.2f}秒")
                    # 关闭进度窗口
                    progress_win.destroy()
                    preview_win.destroy()
                    # 显示完成提示
                    messagebox.showinfo("完成", f"PDF保存成功 (无压缩版本)\n文件已保存到:\n{output_path}")
                except Exception as e:
                    progress_win.destroy()
                    messagebox.showerror("错误", f"保存失败:\n{str(e)}")

        # 启动保存线程
        save_thread = threading.Thread(target=save_in_thread, daemon=True)
        save_thread.start()


if __name__ == "__main__":
    root = tk.Tk()
    # 隐藏主窗口，先显示登录对话框
    root.withdraw()
    login_dialog = LoginDialog(root, title="登录")
    if not login_dialog.result:
        root.destroy()
        exit()
    username, _ = login_dialog.result
    root.deiconify()
    app = PPTMonitorGUI(root, current_user=username)
    root.mainloop()

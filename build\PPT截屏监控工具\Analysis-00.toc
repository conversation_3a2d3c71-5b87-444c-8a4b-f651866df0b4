(['D:\\pythonProject\\ppt_capture\\main_no_compression.py'],
 ['D:\\pythonProject\\ppt_capture'],
 [],
 [('C:\\Program Files\\Python38\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('c:\\program files\\python38\\lib\\site-packages\\pygame\\__pyinstaller', 0),
  ('c:\\program '
   'files\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('c:\\program '
   'files\\python38\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.8.10 (tags/v3.8.10:3d8993a, May  3 2021, 11:48:03) [MSC v.1928 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main_no_compression',
   'D:\\pythonProject\\ppt_capture\\main_no_compression.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'c:\\program files\\python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python38\\lib\\contextlib.py', 'PYMODULE'),
  ('configparser',
   'c:\\program files\\python38\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile', 'c:\\program files\\python38\\lib\\zipfile.py', 'PYMODULE'),
  ('argparse', 'c:\\program files\\python38\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python38\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'c:\\program files\\python38\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python38\\lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'c:\\program files\\python38\\lib\\py_compile.py', 'PYMODULE'),
  ('lzma', 'c:\\program files\\python38\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'c:\\program files\\python38\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'c:\\program files\\python38\\lib\\bz2.py', 'PYMODULE'),
  ('struct', 'c:\\program files\\python38\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python38\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'c:\\program files\\python38\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python38\\lib\\gzip.py', 'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python38\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'c:\\program files\\python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email', 'c:\\program files\\python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'c:\\program files\\python38\\lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'c:\\program files\\python38\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'c:\\program files\\python38\\lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'c:\\program files\\python38\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python38\\lib\\selectors.py', 'PYMODULE'),
  ('random', 'c:\\program files\\python38\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python38\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'c:\\program files\\python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'c:\\program files\\python38\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python38\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'c:\\program files\\python38\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'c:\\program files\\python38\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'c:\\program files\\python38\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'c:\\program files\\python38\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python38\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'c:\\program files\\python38\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'c:\\program files\\python38\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'c:\\program files\\python38\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'c:\\program files\\python38\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'c:\\program files\\python38\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('pkg_resources',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('typing', 'c:\\program files\\python38\\lib\\typing.py', 'PYMODULE'),
  ('__future__', 'c:\\program files\\python38\\lib\\__future__.py', 'PYMODULE'),
  ('packaging.utils',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess', 'c:\\program files\\python38\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python38\\lib\\signal.py', 'PYMODULE'),
  ('packaging.specifiers',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'c:\\program files\\python38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('packaging._structures',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'c:\\program files\\python38\\lib\\ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'c:\\program files\\python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\program files\\python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'c:\\program files\\python38\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid', 'c:\\program files\\python38\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util',
   'c:\\program files\\python38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'c:\\program files\\python38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\program files\\python38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('netbios',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('pdb', 'c:\\program files\\python38\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'c:\\program files\\python38\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'c:\\program files\\python38\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server',
   'c:\\program files\\python38\\lib\\http\\server.py',
   'PYMODULE'),
  ('http', 'c:\\program files\\python38\\lib\\http\\__init__.py', 'PYMODULE'),
  ('socketserver',
   'c:\\program files\\python38\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes', 'c:\\program files\\python38\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.client',
   'c:\\program files\\python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl', 'c:\\program files\\python38\\lib\\ssl.py', 'PYMODULE'),
  ('html', 'c:\\program files\\python38\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\program files\\python38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\program files\\python38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'c:\\program files\\python38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'c:\\program files\\python38\\lib\\tty.py', 'PYMODULE'),
  ('runpy', 'c:\\program files\\python38\\lib\\runpy.py', 'PYMODULE'),
  ('shlex', 'c:\\program files\\python38\\lib\\shlex.py', 'PYMODULE'),
  ('readline',
   'c:\\program files\\python38\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'c:\\program files\\python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('logging.handlers',
   'c:\\program files\\python38\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32con',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('smtplib', 'c:\\program files\\python38\\lib\\smtplib.py', 'PYMODULE'),
  ('hmac', 'c:\\program files\\python38\\lib\\hmac.py', 'PYMODULE'),
  ('queue', 'c:\\program files\\python38\\lib\\queue.py', 'PYMODULE'),
  ('pyreadline3.keysyms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'c:\\program files\\python38\\lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('glob', 'c:\\program files\\python38\\lib\\glob.py', 'PYMODULE'),
  ('code', 'c:\\program files\\python38\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\program files\\python38\\lib\\codeop.py', 'PYMODULE'),
  ('dis', 'c:\\program files\\python38\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'c:\\program files\\python38\\lib\\opcode.py', 'PYMODULE'),
  ('bdb', 'c:\\program files\\python38\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'c:\\program files\\python38\\lib\\cmd.py', 'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\program files\\python38\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\program files\\python38\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\program files\\python38\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\program files\\python38\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'c:\\program files\\python38\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\program files\\python38\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\program files\\python38\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'c:\\program files\\python38\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\program files\\python38\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\program files\\python38\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils',
   'c:\\program files\\python38\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\program files\\python38\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\program files\\python38\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.shell',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('pywintypes',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32com.server',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\program files\\python38\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('pythoncom',
   'c:\\program files\\python38\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'c:\\program files\\python38\\lib\\sysconfig.py', 'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('imp', 'c:\\program files\\python38\\lib\\imp.py', 'PYMODULE'),
  ('inspect', 'c:\\program files\\python38\\lib\\inspect.py', 'PYMODULE'),
  ('tempfile', 'c:\\program files\\python38\\lib\\tempfile.py', 'PYMODULE'),
  ('plistlib', 'c:\\program files\\python38\\lib\\plistlib.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\program files\\python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\program files\\python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'c:\\program files\\python38\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\program files\\python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\program files\\python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\program files\\python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'c:\\program files\\python38\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'c:\\program files\\python38\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'c:\\program files\\python38\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'c:\\program files\\python38\\lib\\netrc.py', 'PYMODULE'),
  ('http.cookiejar',
   'c:\\program files\\python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\program files\\python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'c:\\program files\\python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\program files\\python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\program files\\python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\program files\\python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\program files\\python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('platform', 'c:\\program files\\python38\\lib\\platform.py', 'PYMODULE'),
  ('pkgutil', 'c:\\program files\\python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'c:\\program files\\python38\\lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\program files\\python38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\program files\\python38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\program files\\python38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\program files\\python38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\program files\\python38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\program files\\python38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'c:\\program files\\python38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'c:\\program files\\python38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'c:\\program files\\python38\\lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('decimal', 'c:\\program files\\python38\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python38\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'c:\\program files\\python38\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers', 'c:\\program files\\python38\\lib\\numbers.py', 'PYMODULE'),
  ('multiprocessing.context',
   'c:\\program files\\python38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\program files\\python38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\program files\\python38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\program files\\python38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\program files\\python38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\program files\\python38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\program files\\python38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\program files\\python38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\program files\\python38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\program files\\python38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\program files\\python38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'c:\\program files\\python38\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\program files\\python38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\program files\\python38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing',
   'c:\\program files\\python38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\program files\\python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep', 'c:\\program files\\python38\\lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('skimage.metrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\__init__.py',
   'PYMODULE'),
  ('skimage',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\__init__.py',
   'PYMODULE'),
  ('skimage._shared.tester',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\tester.py',
   'PYMODULE'),
  ('skimage.data',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\__init__.py',
   'PYMODULE'),
  ('skimage.data._registry',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\_registry.py',
   'PYMODULE'),
  ('skimage.data._fetchers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\_fetchers.py',
   'PYMODULE'),
  ('skimage.io',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\__init__.py',
   'PYMODULE'),
  ('skimage.io._plugins.tifffile_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\tifffile_plugin.py',
   'PYMODULE'),
  ('tifffile',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\__init__.py',
   'PYMODULE'),
  ('tifffile.tifffile',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\tifffile.py',
   'PYMODULE'),
  ('doctest', 'c:\\program files\\python38\\lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'c:\\program files\\python38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\program files\\python38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\program files\\python38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\program files\\python38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\program files\\python38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\program files\\python38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\program files\\python38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\program files\\python38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'c:\\program files\\python38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\program files\\python38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\program files\\python38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\program files\\python38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\program files\\python38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\program files\\python38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\program files\\python38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\program files\\python38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\program files\\python38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\program files\\python38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\program files\\python38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\program files\\python38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\program files\\python38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\program files\\python38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\program files\\python38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\program files\\python38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\program files\\python38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\program files\\python38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\program files\\python38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\program files\\python38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\program files\\python38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\program files\\python38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\program files\\python38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\program files\\python38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\program files\\python38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\program files\\python38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\program files\\python38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\program files\\python38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\program files\\python38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\program files\\python38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('difflib', 'c:\\program files\\python38\\lib\\difflib.py', 'PYMODULE'),
  ('defusedxml.ElementTree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'c:\\program files\\python38\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'c:\\program files\\python38\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'c:\\program files\\python38\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'c:\\program files\\python38\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'c:\\program files\\python38\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'c:\\program files\\python38\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'c:\\program files\\python38\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'c:\\program files\\python38\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'c:\\program files\\python38\\lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\program files\\python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('fractions', 'c:\\program files\\python38\\lib\\fractions.py', 'PYMODULE'),
  ('tifffile.geodb',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\geodb.py',
   'PYMODULE'),
  ('lxml',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('bs4',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._warnings',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4._typing',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'c:\\program files\\python38\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('bs4.filter',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.element',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4.css',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('soupsieve',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'c:\\program files\\python38\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('bs4.dammit',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('charset_normalizer',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'c:\\program files\\python38\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('html.parser',
   'c:\\program files\\python38\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'c:\\program files\\python38\\lib\\_markupbase.py',
   'PYMODULE'),
  ('bs4.builder',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'c:\\program files\\python38\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('cgi', 'c:\\program files\\python38\\lib\\cgi.py', 'PYMODULE'),
  ('lxml.cssselect',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\program files\\python38\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\program files\\python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\program files\\python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\program files\\python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'c:\\program files\\python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('numpy.typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.core',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.records',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\program files\\python38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy._globals',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.version',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'c:\\program files\\python38\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\program files\\python38\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\program files\\python38\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('ipaddress', 'c:\\program files\\python38\\lib\\ipaddress.py', 'PYMODULE'),
  ('numpy.testing._private',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\program files\\python38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\program files\\python38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('json', 'c:\\program files\\python38\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python38\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('tifffile._imagecodecs',
   'c:\\program files\\python38\\lib\\site-packages\\tifffile\\_imagecodecs.py',
   'PYMODULE'),
  ('skimage.io._plugins.simpleitk_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\simpleitk_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.pil_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\pil_plugin.py',
   'PYMODULE'),
  ('skimage.util',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\__init__.py',
   'PYMODULE'),
  ('skimage.util.unique',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\unique.py',
   'PYMODULE'),
  ('skimage.util.shape',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\shape.py',
   'PYMODULE'),
  ('skimage.util.noise',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\noise.py',
   'PYMODULE'),
  ('skimage._shared.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\utils.py',
   'PYMODULE'),
  ('skimage._shared._warnings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\_warnings.py',
   'PYMODULE'),
  ('skimage.util.compare',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\compare.py',
   'PYMODULE'),
  ('skimage.util.arraycrop',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\arraycrop.py',
   'PYMODULE'),
  ('skimage.util.apply_parallel',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\apply_parallel.py',
   'PYMODULE'),
  ('skimage.util._regular_grid',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_regular_grid.py',
   'PYMODULE'),
  ('skimage.util._map_array',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_map_array.py',
   'PYMODULE'),
  ('skimage.util._montage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_montage.py',
   'PYMODULE'),
  ('skimage.util._label',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\_label.py',
   'PYMODULE'),
  ('skimage.util._invert',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\_invert.py',
   'PYMODULE'),
  ('skimage.util._slice_along_axes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_slice_along_axes.py',
   'PYMODULE'),
  ('skimage.io._plugins.matplotlib_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\matplotlib_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.imread_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\imread_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.imageio_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\imageio_plugin.py',
   'PYMODULE'),
  ('imageio.v3',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\v3.py',
   'PYMODULE'),
  ('imageio',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\__init__.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile_v3',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\tifffile_v3.py',
   'PYMODULE'),
  ('imageio.typing',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\typing.py',
   'PYMODULE'),
  ('imageio.core.v3_plugin_api',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\v3_plugin_api.py',
   'PYMODULE'),
  ('imageio.core.request',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\request.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.swf',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\swf.py',
   'PYMODULE'),
  ('imageio.plugins.spe',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\spe.py',
   'PYMODULE'),
  ('imageio.plugins.simpleitk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\simpleitk.py',
   'PYMODULE'),
  ('imageio.plugins.rawpy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\rawpy.py',
   'PYMODULE'),
  ('imageio.plugins.pyav',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\pyav.py',
   'PYMODULE'),
  ('imageio.plugins.pillowmulti',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillowmulti.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'c:\\program files\\python38\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._typing',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_legacy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillow_legacy.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_info',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillow_info.py',
   'PYMODULE'),
  ('imageio.plugins.pillow',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\pillow.py',
   'PYMODULE'),
  ('imageio.plugins.opencv',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\opencv.py',
   'PYMODULE'),
  ('imageio.plugins.npz',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\npz.py',
   'PYMODULE'),
  ('imageio.plugins.lytro',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\lytro.py',
   'PYMODULE'),
  ('imageio.plugins.grab',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\grab.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('imageio.plugins.gdal',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\gdal.py',
   'PYMODULE'),
  ('imageio.plugins.freeimagemulti',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\freeimagemulti.py',
   'PYMODULE'),
  ('imageio.plugins.freeimage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\freeimage.py',
   'PYMODULE'),
  ('imageio.plugins.fits',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\fits.py',
   'PYMODULE'),
  ('imageio.plugins.ffmpeg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\ffmpeg.py',
   'PYMODULE'),
  ('imageio.plugins.feisem',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\feisem.py',
   'PYMODULE'),
  ('imageio.plugins.example',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\example.py',
   'PYMODULE'),
  ('imageio.plugins.dicom',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\dicom.py',
   'PYMODULE'),
  ('imageio.plugins.bsdf',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\bsdf.py',
   'PYMODULE'),
  ('imageio.plugins._tifffile',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_tifffile.py',
   'PYMODULE'),
  ('imageio.plugins._swf',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\plugins\\_swf.py',
   'PYMODULE'),
  ('imageio.plugins._freeimage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_freeimage.py',
   'PYMODULE'),
  ('imageio.plugins._dicom',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_dicom.py',
   'PYMODULE'),
  ('imageio.plugins._bsdf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\_bsdf.py',
   'PYMODULE'),
  ('imageio.config',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\config\\__init__.py',
   'PYMODULE'),
  ('imageio.config.plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\config\\plugins.py',
   'PYMODULE'),
  ('imageio.core.legacy_plugin_wrapper',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\legacy_plugin_wrapper.py',
   'PYMODULE'),
  ('imageio.config.extensions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\config\\extensions.py',
   'PYMODULE'),
  ('imageio.plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\plugins\\__init__.py',
   'PYMODULE'),
  ('imageio.v2',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\v2.py',
   'PYMODULE'),
  ('imageio.core.util',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\util.py',
   'PYMODULE'),
  ('imageio.core',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\__init__.py',
   'PYMODULE'),
  ('imageio.core.format',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\format.py',
   'PYMODULE'),
  ('imageio.core.fetching',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\imageio\\core\\fetching.py',
   'PYMODULE'),
  ('imageio.core.findlib',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\findlib.py',
   'PYMODULE'),
  ('imageio.core.imopen',
   'c:\\program files\\python38\\lib\\site-packages\\imageio\\core\\imopen.py',
   'PYMODULE'),
  ('skimage.io._plugins.gdal_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\gdal_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins.fits_plugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\fits_plugin.py',
   'PYMODULE'),
  ('skimage.io._plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\__init__.py',
   'PYMODULE'),
  ('skimage.io._image_stack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_image_stack.py',
   'PYMODULE'),
  ('skimage.io._io',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\_io.py',
   'PYMODULE'),
  ('skimage.io.util',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\util.py',
   'PYMODULE'),
  ('skimage.color.colorconv',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\colorconv.py',
   'PYMODULE'),
  ('skimage.color',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\__init__.py',
   'PYMODULE'),
  ('skimage.color.delta_e',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\delta_e.py',
   'PYMODULE'),
  ('skimage.color.colorlabel',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\colorlabel.py',
   'PYMODULE'),
  ('skimage.color.rgb_colors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\rgb_colors.py',
   'PYMODULE'),
  ('skimage.color.adapt_rgb',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\adapt_rgb.py',
   'PYMODULE'),
  ('scipy.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._testutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_testutils.py',
   'PYMODULE'),
  ('scipy._lib',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\__init__.py',
   'PYMODULE'),
  ('scipy._lib.doccer',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\doccer.py',
   'PYMODULE'),
  ('scipy._lib._pep440',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\_pep440.py',
   'PYMODULE'),
  ('scipy.linalg.matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg.flinalg',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\flinalg.py',
   'PYMODULE'),
  ('scipy.linalg._flinalg_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_flinalg_py.py',
   'PYMODULE'),
  ('scipy.linalg.special_matrices',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\special_matrices.py',
   'PYMODULE'),
  ('scipy.linalg.misc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\misc.py',
   'PYMODULE'),
  ('scipy.linalg.basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\basic.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_schur',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_svd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_qr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_lu',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg.decomp_cholesky',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg.decomp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\decomp.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cossin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_cossin.py',
   'PYMODULE'),
  ('scipy._lib._util',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\_util.py',
   'PYMODULE'),
  ('scipy.sparse',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.sputils',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\sputils.py',
   'PYMODULE'),
  ('scipy.sparse._sputils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_sputils.py',
   'PYMODULE'),
  ('scipy.sparse.sparsetools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\sparsetools.py',
   'PYMODULE'),
  ('scipy.sparse.lil',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\lil.py',
   'PYMODULE'),
  ('scipy.sparse.extract',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\extract.py',
   'PYMODULE'),
  ('scipy.sparse.dok',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\dok.py',
   'PYMODULE'),
  ('scipy.sparse.dia',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\dia.py',
   'PYMODULE'),
  ('scipy.sparse.data',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\data.py',
   'PYMODULE'),
  ('scipy.sparse._data',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_data.py',
   'PYMODULE'),
  ('scipy.sparse.csr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\csr.py',
   'PYMODULE'),
  ('scipy.sparse.csc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\csc.py',
   'PYMODULE'),
  ('scipy.sparse.coo',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\coo.py',
   'PYMODULE'),
  ('scipy.sparse.construct',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\construct.py',
   'PYMODULE'),
  ('scipy.sparse.compressed',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\compressed.py',
   'PYMODULE'),
  ('scipy.sparse._compressed',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_compressed.py',
   'PYMODULE'),
  ('scipy.sparse._index',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_index.py',
   'PYMODULE'),
  ('scipy.sparse.bsr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\bsr.py',
   'PYMODULE'),
  ('scipy.sparse.base',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\base.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._validation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_validation.py',
   'PYMODULE'),
  ('scipy.sparse.csgraph._laplacian',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_laplacian.py',
   'PYMODULE'),
  ('scipy.sparse.linalg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.eigen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\eigen.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.interface',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.dsolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\dsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg.isolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\isolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._expm_multiply',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_expm_multiply.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._norm',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_norm.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._onenormest',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_onenormest.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen._svds',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\_svds.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._svdp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_svdp.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._propack', '-', 'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg.lobpcg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\lobpcg.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.lobpcg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._eigen.arpack.arpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\arpack.py',
   'PYMODULE'),
  ('scipy._lib._threadsafety',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_threadsafety.py',
   'PYMODULE'),
  ('scipy._lib.decorator',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\decorator.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._interface',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_interface.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve._add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._dsolve.linsolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\linsolve.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\__init__.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.tfqmr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tfqmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.utils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\utils.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve._gcrotmk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_gcrotmk.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsmr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsmr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lsqr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lsqr.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.lgmres',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\lgmres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.minres',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\minres.py',
   'PYMODULE'),
  ('scipy.sparse.linalg._isolve.iterative',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\iterative.py',
   'PYMODULE'),
  ('scipy.sparse._arrays',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_arrays.py',
   'PYMODULE'),
  ('scipy.sparse._matrix_io',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_matrix_io.py',
   'PYMODULE'),
  ('scipy.sparse._extract',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_extract.py',
   'PYMODULE'),
  ('scipy.sparse._construct',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_construct.py',
   'PYMODULE'),
  ('scipy.sparse._bsr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_bsr.py',
   'PYMODULE'),
  ('scipy.sparse._dia',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_dia.py',
   'PYMODULE'),
  ('scipy.sparse._coo',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_coo.py',
   'PYMODULE'),
  ('scipy.sparse._dok',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_dok.py',
   'PYMODULE'),
  ('scipy.sparse._lil',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_lil.py',
   'PYMODULE'),
  ('scipy.sparse._csc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_csc.py',
   'PYMODULE'),
  ('scipy.sparse._csr',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_csr.py',
   'PYMODULE'),
  ('scipy.sparse._spfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_spfuncs.py',
   'PYMODULE'),
  ('scipy.sparse._base',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\sparse\\_base.py',
   'PYMODULE'),
  ('numpy.random',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('scipy.linalg._sketches',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_sketches.py',
   'PYMODULE'),
  ('scipy.linalg._procrustes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_procrustes.py',
   'PYMODULE'),
  ('scipy.linalg._solvers',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_solvers.py',
   'PYMODULE'),
  ('scipy.linalg._special_matrices',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_special_matrices.py',
   'PYMODULE'),
  ('scipy.special',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\__init__.py',
   'PYMODULE'),
  ('scipy.special.spfun_stats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\spfun_stats.py',
   'PYMODULE'),
  ('scipy.special.sf_error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\sf_error.py',
   'PYMODULE'),
  ('scipy.special.specfun',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\specfun.py',
   'PYMODULE'),
  ('scipy.special.orthogonal',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\orthogonal.py',
   'PYMODULE'),
  ('scipy.special.basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\special\\basic.py',
   'PYMODULE'),
  ('scipy.special.add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\add_newdocs.py',
   'PYMODULE'),
  ('scipy.special._add_newdocs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_add_newdocs.py',
   'PYMODULE'),
  ('scipy.special._spherical_bessel',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_spherical_bessel.py',
   'PYMODULE'),
  ('scipy.special._lambertw',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_lambertw.py',
   'PYMODULE'),
  ('scipy.special._ellip_harm',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_ellip_harm.py',
   'PYMODULE'),
  ('scipy.integrate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate.quadpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\quadpack.py',
   'PYMODULE'),
  ('scipy.integrate.odepack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\odepack.py',
   'PYMODULE'),
  ('scipy.integrate.vode',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\vode.py',
   'PYMODULE'),
  ('scipy.integrate.lsoda',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate.dop',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\dop.py',
   'PYMODULE'),
  ('scipy.integrate._quad_vec',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_quad_vec.py',
   'PYMODULE'),
  ('scipy.integrate._ivp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\__init__.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.base',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\base.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\common.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.lsoda',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\lsoda.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.bdf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\bdf.py',
   'PYMODULE'),
  ('scipy.optimize._numdiff',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_numdiff.py',
   'PYMODULE'),
  ('scipy.optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize.zeros',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\zeros.py',
   'PYMODULE'),
  ('scipy.optimize.tnc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\tnc.py',
   'PYMODULE'),
  ('scipy.optimize.slsqp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\slsqp.py',
   'PYMODULE'),
  ('scipy.optimize.optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\optimize.py',
   'PYMODULE'),
  ('scipy.optimize.nonlin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\nonlin.py',
   'PYMODULE'),
  ('scipy.optimize.moduleTNC',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\moduleTNC.py',
   'PYMODULE'),
  ('scipy.optimize.minpack2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\minpack2.py',
   'PYMODULE'),
  ('scipy.optimize.minpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\minpack.py',
   'PYMODULE'),
  ('scipy.optimize.linesearch',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\linesearch.py',
   'PYMODULE'),
  ('scipy.optimize._linesearch',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linesearch.py',
   'PYMODULE'),
  ('scipy.optimize.lbfgsb',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\lbfgsb.py',
   'PYMODULE'),
  ('scipy.optimize.cobyla',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\cobyla.py',
   'PYMODULE'),
  ('scipy.optimize._milp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_milp.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_highs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_highs.py',
   'PYMODULE'),
  ('scipy.optimize._highs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_highs\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._direct_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_direct_py.py',
   'PYMODULE'),
  ('scipy.optimize._qap',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_qap.py',
   'PYMODULE'),
  ('scipy.optimize._dual_annealing',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_dual_annealing.py',
   'PYMODULE'),
  ('scipy.optimize._shgo',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_shgo.py',
   'PYMODULE'),
  ('scipy.stats.qmc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\qmc.py',
   'PYMODULE'),
  ('scipy.stats._qmc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_qmc.py',
   'PYMODULE'),
  ('scipy.spatial.distance',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\distance.py',
   'PYMODULE'),
  ('scipy._lib.deprecation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\deprecation.py',
   'PYMODULE'),
  ('scipy.stats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\__init__.py',
   'PYMODULE'),
  ('scipy.stats.stats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\stats.py',
   'PYMODULE'),
  ('scipy.stats.statlib',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\statlib.py',
   'PYMODULE'),
  ('scipy.stats.mvn',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\mvn.py',
   'PYMODULE'),
  ('scipy.stats.mstats_extras',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats._mstats_extras',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_mstats_extras.py',
   'PYMODULE'),
  ('scipy.stats.mstats_basic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.morestats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\morestats.py',
   'PYMODULE'),
  ('scipy.stats.kde',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\kde.py',
   'PYMODULE'),
  ('scipy.stats.biasedurn',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\biasedurn.py',
   'PYMODULE'),
  ('scipy.stats._fit',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_fit.py',
   'PYMODULE'),
  ('scipy.stats._mannwhitneyu',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_mannwhitneyu.py',
   'PYMODULE'),
  ('scipy.stats._axis_nan_policy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py',
   'PYMODULE'),
  ('scipy._lib._docscrape',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_docscrape.py',
   'PYMODULE'),
  ('scipy.stats._page_trend_test',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_page_trend_test.py',
   'PYMODULE'),
  ('scipy.stats._rvs_sampling',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_rvs_sampling.py',
   'PYMODULE'),
  ('scipy.stats._hypotests',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_hypotests.py',
   'PYMODULE'),
  ('scipy.fft',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.helper',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\helper.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\__init__.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.realtransforms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._pocketfft.basic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\basic.py',
   'PYMODULE'),
  ('numpy.fft',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('scipy.fft._backend',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_backend.py',
   'PYMODULE'),
  ('scipy._lib.uarray',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\uarray.py',
   'PYMODULE'),
  ('scipy._lib._uarray',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_uarray\\__init__.py',
   'PYMODULE'),
  ('scipy._lib._uarray._backend',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_uarray\\_backend.py',
   'PYMODULE'),
  ('scipy.fft._helper',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_helper.py',
   'PYMODULE'),
  ('scipy.fft._fftlog_multimethods',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_fftlog_multimethods.py',
   'PYMODULE'),
  ('scipy.fft._fftlog',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_fftlog.py',
   'PYMODULE'),
  ('scipy.fft._realtransforms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_realtransforms.py',
   'PYMODULE'),
  ('scipy.fft._basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\fft\\_basic.py',
   'PYMODULE'),
  ('scipy.stats._common',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_common.py',
   'PYMODULE'),
  ('scipy.stats._entropy',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_entropy.py',
   'PYMODULE'),
  ('scipy.stats._resampling',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_resampling.py',
   'PYMODULE'),
  ('scipy.stats.contingency',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\contingency.py',
   'PYMODULE'),
  ('scipy._lib._bunch',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\_lib\\_bunch.py',
   'PYMODULE'),
  ('scipy.stats._odds_ratio',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_odds_ratio.py',
   'PYMODULE'),
  ('scipy.stats._crosstab',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_crosstab.py',
   'PYMODULE'),
  ('scipy.stats._relative_risk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_relative_risk.py',
   'PYMODULE'),
  ('scipy.stats._rcont',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_rcont\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._covariance',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_covariance.py',
   'PYMODULE'),
  ('scipy.stats._multivariate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_multivariate.py',
   'PYMODULE'),
  ('scipy.stats.mstats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\mstats.py',
   'PYMODULE'),
  ('scipy.stats._kde',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_kde.py',
   'PYMODULE'),
  ('scipy.stats._binned_statistic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_binned_statistic.py',
   'PYMODULE'),
  ('scipy.stats._binomtest',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_binomtest.py',
   'PYMODULE'),
  ('scipy.stats._morestats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_morestats.py',
   'PYMODULE'),
  ('scipy.stats._distn_infrastructure',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_distn_infrastructure.py',
   'PYMODULE'),
  ('scipy.stats._constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_constants.py',
   'PYMODULE'),
  ('scipy._lib._finite_differences',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_finite_differences.py',
   'PYMODULE'),
  ('scipy.stats._distr_params',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_distr_params.py',
   'PYMODULE'),
  ('scipy.stats._variation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_variation.py',
   'PYMODULE'),
  ('scipy.stats._stats_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_stats_py.py',
   'PYMODULE'),
  ('scipy.stats._stats_mstats_common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_stats_mstats_common.py',
   'PYMODULE'),
  ('scipy.ndimage._measurements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_measurements.py',
   'PYMODULE'),
  ('scipy.ndimage._morphology',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_morphology.py',
   'PYMODULE'),
  ('scipy.ndimage._filters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_filters.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_docstrings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_ni_docstrings.py',
   'PYMODULE'),
  ('scipy.ndimage._ni_support',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_ni_support.py',
   'PYMODULE'),
  ('scipy.ndimage',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\__init__.py',
   'PYMODULE'),
  ('scipy.ndimage.morphology',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\morphology.py',
   'PYMODULE'),
  ('scipy.ndimage.measurements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\measurements.py',
   'PYMODULE'),
  ('scipy.ndimage.interpolation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage.fourier',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\fourier.py',
   'PYMODULE'),
  ('scipy.ndimage.filters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\filters.py',
   'PYMODULE'),
  ('scipy.ndimage._interpolation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_interpolation.py',
   'PYMODULE'),
  ('scipy.ndimage._fourier',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_fourier.py',
   'PYMODULE'),
  ('scipy.stats._mstats_basic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_mstats_basic.py',
   'PYMODULE'),
  ('scipy.stats.distributions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\distributions.py',
   'PYMODULE'),
  ('scipy.stats._levy_stable',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_levy_stable\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\__init__.py',
   'PYMODULE'),
  ('scipy.interpolate.rbf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\rbf.py',
   'PYMODULE'),
  ('scipy.interpolate.polyint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\polyint.py',
   'PYMODULE'),
  ('scipy.interpolate.ndgriddata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate.interpolate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate.fitpack',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\fitpack.py',
   'PYMODULE'),
  ('scipy.interpolate._rgi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rgi.py',
   'PYMODULE'),
  ('scipy.interpolate._pade',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_pade.py',
   'PYMODULE'),
  ('scipy.interpolate._bsplines',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_bsplines.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_impl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack_impl.py',
   'PYMODULE'),
  ('scipy.interpolate._ndgriddata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_ndgriddata.py',
   'PYMODULE'),
  ('scipy.interpolate._cubic',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_cubic.py',
   'PYMODULE'),
  ('scipy.interpolate._polyint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_polyint.py',
   'PYMODULE'),
  ('scipy.interpolate._rbfinterp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp.py',
   'PYMODULE'),
  ('scipy.interpolate._rbf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rbf.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack2.py',
   'PYMODULE'),
  ('scipy.interpolate._interpolate',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_interpolate.py',
   'PYMODULE'),
  ('scipy.interpolate._fitpack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack_py.py',
   'PYMODULE'),
  ('scipy.stats._discrete_distns',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_discrete_distns.py',
   'PYMODULE'),
  ('scipy.stats._boost',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\__init__.py',
   'PYMODULE'),
  ('scipy.stats._continuous_distns',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_continuous_distns.py',
   'PYMODULE'),
  ('scipy.stats._ksstats',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\stats\\_ksstats.py',
   'PYMODULE'),
  ('scipy.stats._tukeylambda_stats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_tukeylambda_stats.py',
   'PYMODULE'),
  ('scipy._lib._ccallback',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_ccallback.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('scipy.stats._warnings_errors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_warnings_errors.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib.triangulation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\triangulation.py',
   'PYMODULE'),
  ('scipy.optimize._shgo_lib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_shgo_lib\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\__init__.py',
   'PYMODULE'),
  ('scipy.spatial.transform.rotation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\rotation.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_groups',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_groups.py',
   'PYMODULE'),
  ('scipy.constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\__init__.py',
   'PYMODULE'),
  ('scipy.constants.constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\constants.py',
   'PYMODULE'),
  ('scipy.constants.codata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\codata.py',
   'PYMODULE'),
  ('scipy.constants._constants',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\_constants.py',
   'PYMODULE'),
  ('scipy.constants._codata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\constants\\_codata.py',
   'PYMODULE'),
  ('scipy.spatial.transform._rotation_spline',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation_spline.py',
   'PYMODULE'),
  ('scipy.spatial.qhull',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\spatial\\qhull.py',
   'PYMODULE'),
  ('scipy.spatial.kdtree',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\spatial\\kdtree.py',
   'PYMODULE'),
  ('scipy.spatial.ckdtree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\ckdtree.py',
   'PYMODULE'),
  ('scipy.spatial._geometric_slerp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_geometric_slerp.py',
   'PYMODULE'),
  ('scipy.spatial._procrustes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_procrustes.py',
   'PYMODULE'),
  ('scipy.spatial._plotutils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_plotutils.py',
   'PYMODULE'),
  ('scipy.spatial._spherical_voronoi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_spherical_voronoi.py',
   'PYMODULE'),
  ('scipy.spatial._kdtree',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_kdtree.py',
   'PYMODULE'),
  ('scipy.optimize._hessian_update_strategy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_hessian_update_strategy.py',
   'PYMODULE'),
  ('scipy.optimize._constraints',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_constraints.py',
   'PYMODULE'),
  ('scipy.optimize._differentiable_functions',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_differentiable_functions.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.lsq_linear',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\lsq_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.bvls',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\bvls.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf_linear',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf_linear.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.common',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\common.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.least_squares',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\least_squares.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.dogbox',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\dogbox.py',
   'PYMODULE'),
  ('scipy.optimize._lsq.trf',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\trf.py',
   'PYMODULE'),
  ('scipy.optimize._lsq',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._differentialevolution',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_differentialevolution.py',
   'PYMODULE'),
  ('scipy.optimize._linprog',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_util',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_util.py',
   'PYMODULE'),
  ('scipy.optimize._remove_redundancy',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_remove_redundancy.py',
   'PYMODULE'),
  ('scipy.linalg.interpolative',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\interpolative.py',
   'PYMODULE'),
  ('scipy.linalg._interpolative_backend',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_interpolative_backend.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_doc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_doc.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_rs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_rs.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_simplex',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_simplex.py',
   'PYMODULE'),
  ('scipy.optimize._linprog_ip',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_linprog_ip.py',
   'PYMODULE'),
  ('scipy.optimize._basinhopping',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_basinhopping.py',
   'PYMODULE'),
  ('scipy.optimize._nnls',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_nnls.py',
   'PYMODULE'),
  ('scipy.optimize._slsqp_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_slsqp_py.py',
   'PYMODULE'),
  ('scipy.optimize._cobyla_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_cobyla_py.py',
   'PYMODULE'),
  ('scipy.optimize._tnc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_tnc.py',
   'PYMODULE'),
  ('scipy.optimize._lbfgsb_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lbfgsb_py.py',
   'PYMODULE'),
  ('scipy.optimize._minpack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_minpack_py.py',
   'PYMODULE'),
  ('scipy.optimize._root_scalar',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_root_scalar.py',
   'PYMODULE'),
  ('scipy.optimize._zeros_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_zeros_py.py',
   'PYMODULE'),
  ('scipy.optimize._root',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\optimize\\_root.py',
   'PYMODULE'),
  ('scipy.optimize._spectral',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_spectral.py',
   'PYMODULE'),
  ('scipy.optimize._nonlin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_nonlin.py',
   'PYMODULE'),
  ('scipy.optimize._minimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_minimize.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.minimize_trustregion_constr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\minimize_trustregion_constr.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.report',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\report.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.tr_interior_point',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tr_interior_point.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.canonical_constraint',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\canonical_constraint.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.equality_constrained_sqp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\equality_constrained_sqp.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.qp_subproblem',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\qp_subproblem.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_constr.projections',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\projections.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_exact',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_exact.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_krylov',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_krylov.py',
   'PYMODULE'),
  ('scipy.optimize._trlib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trlib\\__init__.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_ncg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_ncg.py',
   'PYMODULE'),
  ('scipy.optimize._trustregion_dogleg',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trustregion_dogleg.py',
   'PYMODULE'),
  ('scipy.optimize._optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_optimize.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.radau',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\radau.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.rk',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\rk.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.ivp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\ivp.py',
   'PYMODULE'),
  ('scipy.integrate._ivp.dop853_coefficients',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_ivp\\dop853_coefficients.py',
   'PYMODULE'),
  ('scipy.integrate._bvp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\_bvp.py',
   'PYMODULE'),
  ('scipy.integrate._ode',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\integrate\\_ode.py',
   'PYMODULE'),
  ('scipy.integrate._quadpack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_quadpack_py.py',
   'PYMODULE'),
  ('scipy.integrate._odepack_py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_odepack_py.py',
   'PYMODULE'),
  ('scipy.integrate._quadrature',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_quadrature.py',
   'PYMODULE'),
  ('scipy.special._spfun_stats',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_spfun_stats.py',
   'PYMODULE'),
  ('scipy.special._orthogonal',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_orthogonal.py',
   'PYMODULE'),
  ('scipy.special._logsumexp',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_logsumexp.py',
   'PYMODULE'),
  ('scipy.special._basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\special\\_basic.py',
   'PYMODULE'),
  ('scipy.special._sf_error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_sf_error.py',
   'PYMODULE'),
  ('scipy.linalg.lapack',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\lapack.py',
   'PYMODULE'),
  ('scipy.linalg.blas',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\blas.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_inv_ssq',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_inv_ssq.py',
   'PYMODULE'),
  ('scipy.linalg._matfuncs_sqrtm',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm.py',
   'PYMODULE'),
  ('scipy.linalg._expm_frechet',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_expm_frechet.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_polar',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_polar.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_schur',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_schur.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qz',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_qz.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_qr',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_qr.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_cholesky',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_cholesky.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_ldl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_ldl.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_lu',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_lu.py',
   'PYMODULE'),
  ('scipy.linalg._decomp',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp.py',
   'PYMODULE'),
  ('scipy.linalg._basic',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\_basic.py',
   'PYMODULE'),
  ('scipy.linalg._decomp_svd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_svd.py',
   'PYMODULE'),
  ('scipy.linalg._misc',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\linalg\\_misc.py',
   'PYMODULE'),
  ('scipy',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\__init__.py',
   'PYMODULE'),
  ('scipy._distributor_init',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_distributor_init.py',
   'PYMODULE'),
  ('scipy.version',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\version.py',
   'PYMODULE'),
  ('scipy.__config__',
   'c:\\program files\\python38\\lib\\site-packages\\scipy\\__config__.py',
   'PYMODULE'),
  ('skimage.io.collection',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\collection.py',
   'PYMODULE'),
  ('skimage.io.sift',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\io\\sift.py',
   'PYMODULE'),
  ('skimage.io.manage_plugins',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\manage_plugins.py',
   'PYMODULE'),
  ('skimage.data._binary_blobs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\_binary_blobs.py',
   'PYMODULE'),
  ('skimage._shared.filters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\filters.py',
   'PYMODULE'),
  ('skimage.util.lookfor',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\lookfor.py',
   'PYMODULE'),
  ('skimage.util.dtype',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\util\\dtype.py',
   'PYMODULE'),
  ('skimage.exposure',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\__init__.py',
   'PYMODULE'),
  ('skimage.exposure.histogram_matching',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\histogram_matching.py',
   'PYMODULE'),
  ('skimage.exposure.exposure',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\exposure.py',
   'PYMODULE'),
  ('skimage.exposure._adapthist',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\_adapthist.py',
   'PYMODULE'),
  ('skimage._shared',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\__init__.py',
   'PYMODULE'),
  ('lazy_loader',
   'c:\\program files\\python38\\lib\\site-packages\\lazy_loader\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.glob',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'c:\\program files\\python38\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('skimage._shared.version_requirements',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\version_requirements.py',
   'PYMODULE'),
  ('skimage.metrics.simple_metrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\simple_metrics.py',
   'PYMODULE'),
  ('skimage.metrics.set_metrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\set_metrics.py',
   'PYMODULE'),
  ('skimage.metrics._variation_of_information',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_variation_of_information.py',
   'PYMODULE'),
  ('skimage.metrics._structural_similarity',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_structural_similarity.py',
   'PYMODULE'),
  ('skimage.metrics._contingency_table',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_contingency_table.py',
   'PYMODULE'),
  ('skimage.metrics._adapted_rand_error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\metrics\\_adapted_rand_error.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'c:\\program files\\python38\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('threading', 'c:\\program files\\python38\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\program files\\python38\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\program files\\python38\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\program files\\python38\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\program files\\python38\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\program files\\python38\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'c:\\program files\\python38\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\program files\\python38\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('fpdf',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\__init__.py',
   'PYMODULE'),
  ('fpdf.util',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\util.py',
   'PYMODULE'),
  ('fpdf.template',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\template.py',
   'PYMODULE'),
  ('fpdf.prefs',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\prefs.py',
   'PYMODULE'),
  ('fpdf.syntax',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\syntax.py',
   'PYMODULE'),
  ('fpdf.html',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\html.py',
   'PYMODULE'),
  ('fpdf.table',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\table.py',
   'PYMODULE'),
  ('fpdf.drawing',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\drawing.py',
   'PYMODULE'),
  ('fpdf.fpdf',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\fpdf.py',
   'PYMODULE'),
  ('fpdf.unicode_script',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\unicode_script.py',
   'PYMODULE'),
  ('fpdf.transitions',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\transitions.py',
   'PYMODULE'),
  ('fpdf.text_region',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\text_region.py',
   'PYMODULE'),
  ('fpdf.svg',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\svg.py',
   'PYMODULE'),
  ('fontTools.pens.basePen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\basePen.py',
   'PYMODULE'),
  ('fontTools.pens',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\__init__.py',
   'PYMODULE'),
  ('fontTools',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\__init__.py',
   'PYMODULE'),
  ('fontTools.subset',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\subset\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.sfnt',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\ttLib\\sfnt.py',
   'PYMODULE'),
  ('fontTools.ttLib.woff2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\woff2.py',
   'PYMODULE'),
  ('fontTools.ttx',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\ttx.py',
   'PYMODULE'),
  ('fontTools.misc.timeTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\timeTools.py',
   'PYMODULE'),
  ('fontTools.unicode',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\unicode.py',
   'PYMODULE'),
  ('fontTools.misc.macCreatorType',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\macCreatorType.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_l_y_f',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_l_y_f.py',
   'PYMODULE'),
  ('fontTools.misc.filenames',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\filenames.py',
   'PYMODULE'),
  ('fontTools.misc.xmlWriter',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\xmlWriter.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.DefaultTable',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\DefaultTable.py',
   'PYMODULE'),
  ('fontTools.misc.vector',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\vector.py',
   'PYMODULE'),
  ('fontTools.misc.fixedTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\fixedTools.py',
   'PYMODULE'),
  ('fontTools.misc.cython',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\cython.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.ttProgram',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\ttProgram.py',
   'PYMODULE'),
  ('fontTools.misc.arrayTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\arrayTools.py',
   'PYMODULE'),
  ('fontTools.misc.sstruct',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\sstruct.py',
   'PYMODULE'),
  ('fontTools.misc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.xmlReader',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\xmlReader.py',
   'PYMODULE'),
  ('fontTools.misc.classifyTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\classifyTools.py',
   'PYMODULE'),
  ('fontTools.misc.plistlib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\plistlib\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.etree',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\misc\\etree.py',
   'PYMODULE'),
  ('fontTools.misc.psCharStrings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\psCharStrings.py',
   'PYMODULE'),
  ('fontTools.encodings.StandardEncoding',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\encodings\\StandardEncoding.py',
   'PYMODULE'),
  ('fontTools.encodings',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\encodings\\__init__.py',
   'PYMODULE'),
  ('fontTools.pens.boundsPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\boundsPen.py',
   'PYMODULE'),
  ('fontTools.misc.textTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\textTools.py',
   'PYMODULE'),
  ('fontTools.colorLib.builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\builder.py',
   'PYMODULE'),
  ('fontTools.colorLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.colorLib.table_builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\table_builder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otConverters',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otConverters.py',
   'PYMODULE'),
  ('fontTools.misc.lazyTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\lazyTools.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.TupleVariation',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\TupleVariation.py',
   'PYMODULE'),
  ('fontTools.colorLib.geometry',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\geometry.py',
   'PYMODULE'),
  ('fontTools.colorLib.errors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\errors.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_P_A_L_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_P_A_L_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_O_L_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_O_L_R_.py',
   'PYMODULE'),
  ('fontTools.misc.treeTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\treeTools.py',
   'PYMODULE'),
  ('fontTools.colorLib.unbuilder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\colorLib\\unbuilder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._n_a_m_e',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_n_a_m_e.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttVisitor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttVisitor.py',
   'PYMODULE'),
  ('fontTools.misc.visitor',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\visitor.py',
   'PYMODULE'),
  ('fontTools.misc.encodingTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\encodingTools.py',
   'PYMODULE'),
  ('fontTools.encodings.codecs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\encodings\\codecs.py',
   'PYMODULE'),
  ('fontTools.varLib.multiVarStore',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\multiVarStore.py',
   'PYMODULE'),
  ('fontTools.misc.iterTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\iterTools.py',
   'PYMODULE'),
  ('fontTools.varLib.builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\builder.py',
   'PYMODULE'),
  ('fontTools.varLib.models',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\models.py',
   'PYMODULE'),
  ('fontTools.designspaceLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.varLib.errors',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\errors.py',
   'PYMODULE'),
  ('fontTools.misc.intTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\intTools.py',
   'PYMODULE'),
  ('fontTools.varLib.varStore',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\varStore.py',
   'PYMODULE'),
  ('fontTools.varLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFFToCFF2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\CFFToCFF2.py',
   'PYMODULE'),
  ('fontTools.varLib.cff',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\varLib\\cff.py',
   'PYMODULE'),
  ('fontTools.pens.t2CharStringPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\t2CharStringPen.py',
   'PYMODULE'),
  ('fontTools.cffLib.specializer',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\specializer.py',
   'PYMODULE'),
  ('fontTools.otlLib.builder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\builder.py',
   'PYMODULE'),
  ('fontTools.otlLib.error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\error.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize.gpos',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\optimize\\gpos.py',
   'PYMODULE'),
  ('fontTools.otlLib.optimize',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\optimize\\__init__.py',
   'PYMODULE'),
  ('fontTools.otlLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.ast',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\feaLib\\ast.py',
   'PYMODULE'),
  ('fontTools.feaLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.feaLib.location',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\location.py',
   'PYMODULE'),
  ('fontTools.feaLib.error',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\error.py',
   'PYMODULE'),
  ('fontTools.varLib.stat',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\stat.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.types',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\types.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.split',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\split.py',
   'PYMODULE'),
  ('fontTools.designspaceLib.statNames',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\designspaceLib\\statNames.py',
   'PYMODULE'),
  ('fontTools.varLib.featureVars',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\featureVars.py',
   'PYMODULE'),
  ('fontTools.misc.dictTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\dictTools.py',
   'PYMODULE'),
  ('fontTools.varLib.mvar',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\mvar.py',
   'PYMODULE'),
  ('fontTools.varLib.merger',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\merger.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTraverse',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTraverse.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_v_a_r.py',
   'PYMODULE'),
  ('fontTools.subset.svg',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\subset\\svg.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_V_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_V_G_.py',
   'PYMODULE'),
  ('fontTools.subset.cff',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\subset\\cff.py',
   'PYMODULE'),
  ('fontTools.subset.util',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\subset\\util.py',
   'PYMODULE'),
  ('fontTools.misc.cliTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\cliTools.py',
   'PYMODULE'),
  ('fontTools.otlLib.maxContextCalc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\otlLib\\maxContextCalc.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otBase',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otBase.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otTables',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otTables.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.otData',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\otData.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttGlyphSet',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttGlyphSet.py',
   'PYMODULE'),
  ('fontTools.pens.pointPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\pointPen.py',
   'PYMODULE'),
  ('fontTools.pens.recordingPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\recordingPen.py',
   'PYMODULE'),
  ('fontTools.feaLib.lookupDebugInfo',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\feaLib\\lookupDebugInfo.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_m_t_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._v_h_e_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_v_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._t_r_a_k',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_t_r_a_k.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._s_b_i_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_s_b_i_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixStrike',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixStrike.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.sbixGlyph',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\sbixGlyph.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_o_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_o_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_r_e_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_r_e_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._p_o_s_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_p_o_s_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.standardGlyphOrder',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\standardGlyphOrder.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._o_p_b_d',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_o_p_b_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_o_r_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_o_r_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_e_t_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_e_t_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._m_a_x_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_m_a_x_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_t_a_g',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_t_a_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_o_c_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_o_c_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._l_c_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_l_c_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._k_e_r_n',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_k_e_r_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_m_t_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_m_t_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_h_e_a',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_h_e_a.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_e_a_d',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_e_a_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._h_d_m_x',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_h_d_m_x.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_c_i_d',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_c_i_d.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._g_a_s_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_g_a_s_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_p_g_m',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_p_g_m.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._f_e_a_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_f_e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_m_a_p',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_m_a_p.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._c_i_d_g',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_c_i_d_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._b_s_l_n',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_b_s_l_n.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_v_a_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_v_a_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables._a_n_k_r',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\_a_n_k_r.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_V_A_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_O_R_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_O_R_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_D_M_X_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_D_M_X_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.V_A_R_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\V_A_R_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_T_F_A_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_T_F_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__5',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__5.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__3',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__3.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__1',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__1.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I__0',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I__0.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_V_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_V_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_S_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_P_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_J_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_J_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_D_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_D_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.T_S_I_B_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\T_S_I_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.asciiTable',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\asciiTable.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_l',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_l.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S__i_l_f',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S__i_l_f.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_T_A_T_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_T_A_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.S_I_N_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\S_I_N_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.O_S_2f_2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\O_S_2f_2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_V_A_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_E_T_A_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_E_T_A_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.M_A_T_H_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\M_A_T_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.L_T_S_H_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\L_T_S_H_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.J_S_T_F_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\J_S_T_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.H_V_A_R_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\H_V_A_R_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_o_c',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_o_c.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G__l_a_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G__l_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_S_U_B_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_S_U_B_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_O_S_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_O_S_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_P_K_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_P_K_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_M_A_P_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_M_A_P_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.G_D_E_F_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\G_D_E_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F__e_a_t',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\F__e_a_t.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.grUtils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\grUtils.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.F_F_T_M_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\F_F_T_M_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D__e_b_g',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\D__e_b_g.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.D_S_I_G_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\D_S_I_G_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F__2',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F__2.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_F_F_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_F_F_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_L_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_L_C_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_L_C_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.BitmapGlyphMetrics',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\BitmapGlyphMetrics.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.C_B_D_T_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\C_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.E_B_D_T_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\E_B_D_T_.py',
   'PYMODULE'),
  ('fontTools.ttLib.tables.B_A_S_E_',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\tables\\B_A_S_E_.py',
   'PYMODULE'),
  ('fontTools.misc.roundTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\roundTools.py',
   'PYMODULE'),
  ('fontTools.config',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\config\\__init__.py',
   'PYMODULE'),
  ('fontTools.misc.configTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\configTools.py',
   'PYMODULE'),
  ('fontTools.ttLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttCollection',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttCollection.py',
   'PYMODULE'),
  ('fontTools.ttLib.ttFont',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\ttFont.py',
   'PYMODULE'),
  ('fontTools.ttLib.reorderGlyphs',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\reorderGlyphs.py',
   'PYMODULE'),
  ('fontTools.ttLib.macUtils',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\ttLib\\macUtils.py',
   'PYMODULE'),
  ('fontTools.misc.macRes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\macRes.py',
   'PYMODULE'),
  ('fontTools.agl',
   'c:\\program files\\python38\\lib\\site-packages\\fontTools\\agl.py',
   'PYMODULE'),
  ('fontTools.cffLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.cffLib.transforms',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\transforms.py',
   'PYMODULE'),
  ('fontTools.cffLib.CFF2ToCFF',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\CFF2ToCFF.py',
   'PYMODULE'),
  ('fontTools.cffLib.width',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\cffLib\\width.py',
   'PYMODULE'),
  ('fontTools.pens.reverseContourPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\reverseContourPen.py',
   'PYMODULE'),
  ('fontTools.pens.filterPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\filterPen.py',
   'PYMODULE'),
  ('fontTools.pens.transformPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\transformPen.py',
   'PYMODULE'),
  ('fontTools.misc.transform',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\transform.py',
   'PYMODULE'),
  ('fontTools.misc.loggingTools',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\loggingTools.py',
   'PYMODULE'),
  ('timeit', 'c:\\program files\\python38\\lib\\timeit.py', 'PYMODULE'),
  ('fontTools.svgLib.path',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\__init__.py',
   'PYMODULE'),
  ('fontTools.svgLib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\__init__.py',
   'PYMODULE'),
  ('fontTools.svgLib.path.shapes',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\shapes.py',
   'PYMODULE'),
  ('fontTools.svgLib.path.parser',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\parser.py',
   'PYMODULE'),
  ('fontTools.svgLib.path.arc',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\svgLib\\path\\arc.py',
   'PYMODULE'),
  ('fpdf.structure_tree',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\structure_tree.py',
   'PYMODULE'),
  ('fpdf.sign',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\sign.py',
   'PYMODULE'),
  ('unittest.mock',
   'c:\\program files\\python38\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('fpdf.recorder',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\recorder.py',
   'PYMODULE'),
  ('fpdf.output',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\output.py',
   'PYMODULE'),
  ('fpdf.outline',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\outline.py',
   'PYMODULE'),
  ('fpdf.line_break',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\line_break.py',
   'PYMODULE'),
  ('fpdf.linearization',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\linearization.py',
   'PYMODULE'),
  ('fpdf.image_parsing',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\image_parsing.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('fpdf.image_datastructures',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fpdf\\image_datastructures.py',
   'PYMODULE'),
  ('fpdf.graphics_state',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\graphics_state.py',
   'PYMODULE'),
  ('fpdf.encryption',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\encryption.py',
   'PYMODULE'),
  ('fpdf.bidi',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\bidi.py',
   'PYMODULE'),
  ('fpdf.annotations',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\annotations.py',
   'PYMODULE'),
  ('fpdf.actions',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\actions.py',
   'PYMODULE'),
  ('fpdf.fonts',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\fonts.py',
   'PYMODULE'),
  ('fontTools.pens.ttGlyphPen',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\pens\\ttGlyphPen.py',
   'PYMODULE'),
  ('fpdf.errors',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\errors.py',
   'PYMODULE'),
  ('fpdf.enums',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\enums.py',
   'PYMODULE'),
  ('fpdf.deprecation',
   'c:\\program files\\python38\\lib\\site-packages\\fpdf\\deprecation.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\program files\\python38\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('pyautogui',
   'c:\\program files\\python38\\lib\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_x11',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_win',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_osx',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE'),
  ('pygetwindow',
   'c:\\program files\\python38\\lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pyrect',
   'c:\\program files\\python38\\lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('mouseinfo',
   'c:\\program files\\python38\\lib\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE'),
  ('pyperclip',
   'c:\\program files\\python38\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('PyQt5',
   'c:\\program files\\python38\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('pyscreeze',
   'c:\\program files\\python38\\lib\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE'),
  ('pymsgbox',
   'c:\\program files\\python38\\lib\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE'),
  ('pymsgbox._native_win',
   'c:\\program files\\python38\\lib\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE'),
  ('pytweening',
   'c:\\program files\\python38\\lib\\site-packages\\pytweening\\__init__.py',
   'PYMODULE'),
  ('numpy',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'c:\\program files\\python38\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE')],
 [('python38.dll', 'c:\\program files\\python38\\python38.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes38.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pywin32_system32\\pywintypes38.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom38.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pywin32_system32\\pythoncom38.dll',
   'BINARY'),
  ('scipy.libs\\libopenblas-802f9ed1179cb9c9b03d67ff79f48187.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy.libs\\libopenblas-802f9ed1179cb9c9b03d67ff79f48187.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4120_64.dll',
   'BINARY'),
  ('_lzma.pyd', 'c:\\program files\\python38\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'c:\\program files\\python38\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'c:\\program files\\python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'c:\\program files\\python38\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'c:\\program files\\python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\program files\\python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'c:\\program files\\python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'c:\\program files\\python38\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'c:\\program files\\python38\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('win32com\\shell\\shell.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32comext\\shell\\shell.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'c:\\program files\\python38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'c:\\program files\\python38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'c:\\program files\\python38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'c:\\program files\\python38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'c:\\program files\\python38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\etree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\_elementpath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\sax.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\objectify.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\charset_normalizer\\md.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\diff.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\html\\_difflib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\builder.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'c:\\program files\\python38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\util\\_remap.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\util\\_remap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\_imaging.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\_imagingmath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\io\\_plugins\\_histograms.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\_histograms.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\io\\_plugins\\_colormixer.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\_colormixer.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\cython_lapack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\cython_blas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_isolve\\_iterative.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\_iterative.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_csparsetools.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\sparse\\_sparsetools.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_decomp_update.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_constants.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_highs\\_highs_constants.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highs\\_highs_wrapper.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_highs\\_highs_wrapper.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_direct.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_qmc_cy.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_sobol.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_hausdorff.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_biasedurn.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_stats_pythran.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_mvn.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_mvn.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_statlib.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_statlib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_nd_image.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\ndimage\\_ni_label.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\interpnd.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\interpnd.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_ppoly.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_bspl.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_bspl.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\dfitpack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\dfitpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\interpolate\\_fitpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\invgauss_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\invgauss_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\skewnorm_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\skewnorm_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nct_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\nct_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncx2_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\ncx2_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\ncf_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\ncf_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\hypergeom_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\hypergeom_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\nbinom_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\nbinom_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\binom_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\binom_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_boost\\beta_ufunc.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_boost\\beta_ufunc.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\stats\\_stats.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\cython_special.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_voronoi.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_qhull.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\spatial\\_ckdtree.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lsap.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_interpolative.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_interpolative.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\__nnls.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\__nnls.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqp.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_slsqp.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_zeros.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_minpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_cobyla.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_cobyla.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack2.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_minpack2.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\optimize\\_group_columns.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_lsoda.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_dop.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_vode.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_quadpack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\integrate\\_odepack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_comb.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_specfun.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_ufuncs.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flinalg.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_flinalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_flapack.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\linalg\\_fblas.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\_fpumode.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy\\_lib\\messagestream.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('skimage\\_shared\\geometry.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\_shared\\geometry.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'c:\\program files\\python38\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('fontTools\\misc\\bezierTools.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\misc\\bezierTools.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('fontTools\\varLib\\iup.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\fontTools\\varLib\\iup.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\_imagingcms.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\_imagingtk.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\_imagingft.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PIL\\_webp.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'c:\\program files\\python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'c:\\program files\\python38\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Program '
   'Files\\Python38\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\program files\\python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll', 'c:\\program files\\python38\\DLLs\\libffi-7.dll', 'BINARY'),
  ('libssl-1_1.dll',
   'c:\\program files\\python38\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'c:\\program files\\python38\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('python3.dll', 'c:\\program files\\python38\\python3.dll', 'BINARY'),
  ('tcl86t.dll', 'c:\\program files\\python38\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'c:\\program files\\python38\\DLLs\\tk86t.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY')],
 [],
 [],
 [('skimage\\__init__.pyi',
   'c:\\program files\\python38\\lib\\site-packages\\skimage\\__init__.pyi',
   'DATA'),
  ('skimage\\data\\__init__.pyi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\data\\__init__.pyi',
   'DATA'),
  ('skimage\\io\\_plugins\\_histograms.cp38-win_amd64.lib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\_histograms.cp38-win_amd64.lib',
   'DATA'),
  ('skimage\\io\\_plugins\\gdal_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\gdal_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\tifffile_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\tifffile_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\imread_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\imread_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\pil_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\pil_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\fits_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\fits_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\imageio_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\imageio_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\simpleitk_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\simpleitk_plugin.ini',
   'DATA'),
  ('skimage\\io\\_plugins\\_colormixer.cp38-win_amd64.lib',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\_colormixer.cp38-win_amd64.lib',
   'DATA'),
  ('skimage\\io\\_plugins\\matplotlib_plugin.ini',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\io\\_plugins\\matplotlib_plugin.ini',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('skimage\\color\\__init__.pyi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\color\\__init__.pyi',
   'DATA'),
  ('scipy.libs\\.load-order-scipy-1.10.1',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\scipy.libs\\.load-order-scipy-1.10.1',
   'DATA'),
  ('skimage\\exposure\\__init__.pyi',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\skimage\\exposure\\__init__.pyi',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\license.terms',
   'c:\\program files\\python38\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'c:\\program files\\python38\\tcl\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tk_data\\button.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'c:\\program files\\python38\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'c:\\program files\\python38\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tk_data\\images\\README',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tk_data\\tclIndex',
   'c:\\program files\\python38\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tk_data\\console.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'c:\\program files\\python38\\tcl\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'c:\\program files\\python38\\tcl\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tk_data\\text.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'c:\\program files\\python38\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'c:\\program files\\python38\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\program '
   'files\\python38\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'c:\\program files\\python38\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'c:\\program files\\python38\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'c:\\program files\\python38\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'c:\\program files\\python38\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'c:\\program files\\python38\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\program '
   'files\\python38\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\config-3.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('cv2\\__init__.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'c:\\program '
   'files\\python38\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'c:\\program files\\python38\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'D:\\pythonProject\\ppt_capture\\build\\PPT截屏监控工具\\base_library.zip',
   'DATA')])

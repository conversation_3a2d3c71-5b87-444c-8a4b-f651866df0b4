<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="13">
            <item index="0" class="java.lang.String" itemvalue="pywin32" />
            <item index="1" class="java.lang.String" itemvalue="python_docx" />
            <item index="2" class="java.lang.String" itemvalue="pandas" />
            <item index="3" class="java.lang.String" itemvalue="selenium" />
            <item index="4" class="java.lang.String" itemvalue="snapshot_selenium" />
            <item index="5" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="6" class="java.lang.String" itemvalue="pyecharts" />
            <item index="7" class="java.lang.String" itemvalue="ddddocr" />
            <item index="8" class="java.lang.String" itemvalue="requests" />
            <item index="9" class="java.lang.String" itemvalue="urllib3" />
            <item index="10" class="java.lang.String" itemvalue="toml" />
            <item index="11" class="java.lang.String" itemvalue="APScheduler" />
            <item index="12" class="java.lang.String" itemvalue="tqdm" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="urllib3.exceptions" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>